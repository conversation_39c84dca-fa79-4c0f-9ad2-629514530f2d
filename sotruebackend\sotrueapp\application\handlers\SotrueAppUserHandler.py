from components.AppProcessHandlerBase import AppProcessHandlerBase
from components.AppSecurity import AppSecurity
from components.AppConfigs import AppConfigs
from components.AppEmail import AppEmail
from components.AppSMSSender import AppSMSS<PERSON>
from components.AppRazorPay import AppRazorPay
from components.AppFirebase import AppFirebase
from components.AppAwsS3 import AppAwsS3
from components.AppExcel import AppExcel
from components.AppAppleSignin import AppAppleSignin
from .SotrueAppGenericCode import SotrueAppGenericCode
from sotrueapp.application.misc.SotrueAppConfig import SotrueAppConfig

import requests
import os
import os.path
import random

class SotrueAppUserHandler(AppProcessHandlerBase):

    def ensure_directory_exists(self, path):
        """Ensure the directory exists, create if it doesn't"""
        if not os.path.exists(path):
            os.makedirs(path, exist_ok=True)
    
    reactions = ["MEH","LIT","ANGER","AUD","BOO","BLADY","BMAN","CELEB","CLAP","DISG","FEAR","SURP","TEASE","TPASS","WEVER","YAY","YUM"]

    def handle_login(self):
        user_mode = True
        if self.params["user_id"].startswith("##uid##_") and self.params["password"].startswith("##pwd##_"):
            user_mode = False
            qparams = [self.params["user_id"],self.params["password"]]
            credentials = self.connDB.execute_prepared_stmt("sotrueappuser","GET_SESSION_CREDS",qparams)
            if credentials:
                uid = self.params["user_id"][8:]
                pwd = self.params["password"][8:]
                key = credentials[0].key.encode("utf-8")
                self.params["user_id"] = AppSecurity.decrypt_str(uid.encode("utf-8"),key)
                self.params["password"] = AppSecurity.decrypt_str(pwd.encode("utf-8"),key)
                self.logger.log_message("###### USER CREDENTIALS #####")
                self.logger.log_message(self.params["user_id"])
                self.logger.log_message(self.params["password"])
            
        qparams = [self.params["user_id"]]
        user_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_CREDENTIALS",qparams)

        if user_data:
            if user_data[0].status != 'ACTIVE':
                response = self.create_error("UE005")
                return(response)            
            if AppSecurity.check_hash(user_data[0].password,self.params["password"]):                            

                qparams = [user_data[0].last_profile]
                profile_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)
                if profile_data[0].status != 'ACTIVE':
                    response = self.create_error("UE005")
                    return(response) 

                if user_mode:
                    encr_data = AppSecurity.encrypt_str(self.params["user_id"])
                    uid = "##uid##_" + encr_data["value"].decode("utf-8")
                    key = encr_data["key"]
                    encr_data = AppSecurity.encrypt_str(self.params["password"],key)
                    passwd = "##pwd##_" + encr_data["value"].decode("utf-8")

                    qparams = [key.decode("utf-8"),uid,passwd]
                    self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_SESSION_CREDS",qparams,replication=False)                
                
                response = dict(user_seq=user_data[0].user_seq,
                                profile_seq=user_data[0].last_profile,
                                user_handle=profile_data[0].user_handle,
                                account_type=profile_data[0].type,                                
                                uid=uid if user_mode else None,
                                pwd=passwd if user_mode else None,
                                max_file_size=60,
                                ui_colour=user_data[0].ui_colour)

                session_data = {"_user_seq":user_data[0].user_seq,"_session_expiry":AppConfigs.session_expiry,
                                "_profile_seq":user_data[0].last_profile,"_email_id":user_data[0].email_id}
                self.session.start_user_session(session_data)                
                #self.tracker.track_login()
                response = self.create_response(response,"UE104")

                self.set_post_processing(SotrueAppGenericCode,SotrueAppGenericCode.update_last_access)
                return(response)
                                
        response = self.create_error("UE006")
        return(response)                                


    def gmail_login(self):

        if not AppSecurity.validate_google(self.params["password"]):
            response = self.create_error("UE078")
            return(response)

        qparams = [self.params["user_id"]]
        user_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_CREDENTIALS",qparams)        

        if user_data:
            if not user_data[0].is_gmail:
                response = self.create_error("UE077")
                return(response)

            if user_data[0].status != 'ACTIVE':
                response = self.create_error("UE005")
                return(response) 

            qparams = [user_data[0].last_profile]
            profile_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)
            if profile_data[0].status != 'ACTIVE':
                response = self.create_error("UE005")
                return(response) 

            encr_data = AppSecurity.encrypt_str(self.params["user_id"])
            uid = "##uid##_" + encr_data["value"].decode("utf-8")
            key = encr_data["key"]
            encr_data = AppSecurity.encrypt_str(user_data[0].is_gmail,key)
            passwd = "##pwd##_" + encr_data["value"].decode("utf-8")
            qparams = [key.decode("utf-8"),uid,passwd]
            self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_SESSION_CREDS",qparams,replication=False)

            session_data = {"_user_seq":user_data[0].user_seq,"_session_expiry":AppConfigs.session_expiry,
                                "_profile_seq":user_data[0].last_profile, "_email_id":user_data[0].email_id}
            self.session.start_user_session(session_data)                
            self.tracker.track_login()

            response = dict(user_seq=user_data[0].user_seq,
                                profile_seq=user_data[0].last_profile,
                                user_handle=profile_data[0].user_handle,
                                account_type=profile_data[0].type,
                                uid=uid,
                                pwd=passwd,
                                max_file_size=60,
                                server_url=SotrueAppConfig.servers[user_data[0].server])
            response = self.create_response(response,"UE104")

            self.set_post_processing(SotrueAppGenericCode,SotrueAppGenericCode.update_last_access)
            return(response)

        response = self.create_error("UE006")
        return(response)


    def apple_login(self):
        user_id = None
        apple_key = None
        if "key" in self.params:
            user_id = self.params["user_id"] if "user_id" in self.params else "none"
            apple_key = self.params["key"]
        elif "password" in self.params:
            self.logger.log_message("Apple User Sign In Token: " + self.params["password"])
            apple_login = AppAppleSignin()
            response = apple_login.get_user_details(self.params["password"])
            self.logger.log_message("Apple Reponse: ")
            self.logger.log_message(response)
            if "error" in response or "id_token" not in response:
                response = self.create_error("UE092")
                return(response)
        
            values = apple_login.decode_user_data(response["id_token"])
            self.logger.log_message("Apple Decoded Values: ")
            self.logger.log_message(values)
            user_id = values["email"] if "email" in values else "none"       

        qparams = [user_id]
        user_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_CREDENTIALS",qparams)        

        if user_data:
            if not user_data[0].is_apple:
                response = self.create_error("UE077")
                return(response)

            if user_data[0].status != 'ACTIVE':
                response = self.create_error("UE005")
                return(response) 

            if apple_key and user_data[0].is_apple != apple_key:
                response = self.create_error("UE093")
                return(response) 

            qparams = [user_data[0].last_profile]
            profile_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)
            if profile_data[0].status != 'ACTIVE':
                response = self.create_error("UE005")
                return(response) 

            encr_data = AppSecurity.encrypt_str(user_id)
            uid = "##uid##_" + encr_data["value"].decode("utf-8")
            key = encr_data["key"]
            encr_data = AppSecurity.encrypt_str(user_data[0].is_apple,key)
            passwd = "##pwd##_" + encr_data["value"].decode("utf-8")
            qparams = [key.decode("utf-8"),uid,passwd]
            self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_SESSION_CREDS",qparams,replication=False)

            session_data = {"_user_seq":user_data[0].user_seq,"_session_expiry":AppConfigs.session_expiry,
                                "_profile_seq":user_data[0].last_profile, "_email_id":user_data[0].email_id}
            self.session.start_user_session(session_data)                
            self.tracker.track_login()

            response = dict(user_seq=user_data[0].user_seq,
                                profile_seq=user_data[0].last_profile,
                                user_handle=profile_data[0].user_handle,
                                account_type=profile_data[0].type,
                                uid=uid,
                                pwd=passwd,
                                max_file_size=60,
                                server_url=SotrueAppConfig.servers[user_data[0].server])
            response = self.create_response(response,"UE104")

            self.set_post_processing(SotrueAppGenericCode,SotrueAppGenericCode.update_last_access)
            return(response)

        response = self.create_error("UE006")
        return(response)


    def handle_register(self):
       
        if "mode" in self.params and self.params["mode"] == "GMAIL":
            if not AppSecurity.validate_google(self.params["password"]):
                response = self.create_error("UE078")
                return(response)

        apple_response = None
        apple_key = None
        if "mode" in self.params and self.params["mode"] == "APPLE":
            self.logger.log_message("Apple User Sign In Token: " + self.params["password"])
            apple_login = AppAppleSignin()
            apple_response = apple_login.get_user_details(self.params["password"])
            self.logger.log_message("Apple Reponse: ")
            self.logger.log_message(apple_response)
            if "error" in apple_response or "id_token" not in apple_response:
                response = self.create_error("UE092")
                return(response)
            apple_key = AppSecurity.get_random_password()

        if "referral_code" in self.params:
            qparams = [self.params['referral_code']]
            ref_user_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_FOR_REFERRAL",qparams)
            if not ref_user_data:
                response = self.create_error("UE062")
                return(response)     

        email = self.params["email"] if "email" in self.params else ""
        full_name = self.params["full_name"] if "full_name" in self.params else ""
        if "mode" in self.params and self.params["mode"] == "APPLE":
            values = apple_login.decode_user_data(apple_response["id_token"])
            self.logger.log_message("Apple Decoded Values: ")
            self.logger.log_message(values)
            email = values["email"] if "email" in values else "<EMAIL>"            

        qparams = [email]
        user_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_EMAIL",qparams)
        if user_data and user_data[0].status != 'PENDING':
            response = self.create_error("UE001")
            return(response)
        elif not self.check_remote_duplicate_user(email,None):
            response = self.create_error("UE001")
            return(response)

        otp = AppSecurity.get_random_otp(4)
        self.logger.log_message("OTP for account activation is : " + str(otp))                

        user_seq = -1
        if not user_data:
            if "mode" in self.params and self.params["mode"] == "GMAIL":
                self.params["password"] = AppSecurity.get_random_password(12)
            elif "mode" in self.params and self.params["mode"] == "APPLE":
                self.params["password"] = apple_key

            qparams = [full_name,email,AppSecurity.hash(self.params["password"]),
                       otp,"PENDING",
                   self.utils.get_cur_timestamp(),2000,AppConfigs.server_key,
                   self.params["password"] if "mode" in self.params and self.params["mode"] == "GMAIL" else None,
                   apple_key if "mode" in self.params and self.params["mode"] == "APPLE" else None,
                   self.params["type"] if "type" in self.params else "NORMAL"]
            user_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_USER_RECORD",qparams)
            if "mode" in self.params and (self.params["mode"] == "GMAIL" or self.params["mode"] == "APPLE"):
                self.params["user_seq"] = user_seq
                self.submit_otp(True)
            elif "type" in self.params and self.params["type"] == "DUMMY":
                self.params["user_seq"] = user_seq
                self.submit_otp(True)
            self.tracker.track_register(email)
        else:
            qparams = [otp,user_data[0].user_seq]
            self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_USER_OTP",qparams)
            user_seq = user_data[0].user_seq

        if "referral_code" in self.params:            
            qparams = ['REFERRAL_PC','ACTIVE']
            referral_value = self.connDB.execute_prepared_stmt("sotrueappuser","GET_CODE_VALUES",qparams)
            qparams = ['REFERRAL_TIME','ACTIVE']
            referral_duration = self.connDB.execute_prepared_stmt("sotrueappuser","GET_CODE_VALUES",qparams)
            
            qparams = [ref_user_data[0].user_seq, user_seq, self.utils.get_cur_timestamp(),
                       "TEMP" if self.params["mode"] != "APPLE" and self.params["mode"] != "GMAIL" else "ACTIVE",
                      referral_value[0].display_value,referral_duration[0].display_value]
            referral_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_USER_REFERAL",qparams)

            qparams = [referral_seq,user_seq]
            self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_REFERAL_CODE_USED",qparams)
            self.tracker.track_referral_code_used(self.get_user(ref_user_data[0].user_seq),self.get_user(user_seq))

        if (self.params["mode"] != "GMAIL" and self.params["mode"] != "APPLE"):            
            if("type" not in self.params or self.params["type"] != "DUMMY"):
                mailer = AppEmail()
                mailer.send_email(email,None,"SoTrue Account Activation",
                    "Welcome to SoTrue!\n\nWe're thrilled to have you join our community!\n\nTo get started, please activate your account using the code below:\n\nYour account activation code: " + str(otp) + "\n\nBest,\nTeam SoTrue");        
                response = self.create_response(dict(user_seq=user_seq),"UE002")   
                return(response)
            else:
                response = self.create_response(dict(user_seq=user_seq),"UE003")   
                return(response)
        else:
            if(self.params["mode"] != "APPLE"):
                response = self.create_response(dict(user_seq=user_seq),"UE003")   
                return(response)
            else:
                response = self.create_response(dict(user_seq=user_seq,email=email,key=apple_key),"UE003")   
                return(response)


    def submit_otp(self,otp_bypass=False,mobile_login=False):
        
        qparams = [self.params["user_seq"]]
        otp_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_OTP",qparams)        

        if otp_bypass or (otp_data and otp_data[0].activation_otp == self.params['otp']):
            qparams = ["ACTIVE","",self.params["user_seq"]]
            self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_USER_STATUS",qparams)
            qparams = ["USER","ACTIVE"]
            role_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_ROLE_SEQ",qparams)
            if role_data:
                qparams = [self.params["user_seq"], role_data[0].role_seq, "ACTIVE"]
                self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_USER_ROLE",qparams)

                handle = "u" + str(AppSecurity.get_random_otp(10))
                while True:
                    qparams = [handle]
                    handle_data = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_HANDLE",qparams)
                    if not handle_data:
                        break
                    else:
                        handle = "u" + str(AppSecurity.get_random_otp(10))                
                        
                qparams = [self.params["user_seq"], handle, self.utils.get_cur_timestamp(), "NOTPAID",
                           "NO",0.00,self.utils.get_cur_timestamp(),"ACTIVE",otp_data[0].full_name,
                           "YES","YES","YES","NO"]
                profile_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_USER_PROFILE",qparams)

                qparams = [profile_seq,"DUMMY"]
                profile_cat_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_USER_CATEGORY",qparams)

                qparams = [profile_seq,self.params["user_seq"]]
                self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_LAST_PROFILE",qparams)

                if otp_data[0].used_referral_seq:
                    qparams = ['ACTIVE',otp_data[0].used_referral_seq]
                    self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_REFERRAL_CODE_STATUS",qparams)
                
            if not mobile_login:
                response = self.create_response(dict(profile_seq=profile_seq),"UE003")   
            else:
                response = self.create_response(dict(profile_seq=profile_seq),"UE102")   
            return(response)
        
        response = self.create_error("UE008")   
        return(response)


    def generate_otp(self):
        mobile = self.params["mobile"]
        print(f"[generate_otp] Starting OTP generation for mobile: {mobile}")

        qparams = [mobile, 'ACTIVE', 'PENDING']
        user_data = self.connDB.execute_prepared_stmt("sotrueappuser", "GET_USER_DETAILS_MOBILE", qparams)
        print(f"[generate_otp] Retrieved user data: {user_data}")

        active_accs = []
        pending_accs = []

        for rec in user_data:
            if rec.status == "ACTIVE":
                active_accs.append(rec)
            else:
                pending_accs.append(rec)

        print(f"[generate_otp] Active accounts: {len(active_accs)}, Pending accounts: {len(pending_accs)}")

        if pending_accs:
            for rec in pending_accs:
                print(f"[generate_otp] Marking pending user {rec.user_seq} as INACTIVE")
                qparams = ['INACTIVE', rec.user_seq]
                self.connDB.execute_prepared_stmt("sotrueappuser", "DELETE_PENDING_ACC", qparams)

        if active_accs:
            otp = AppSecurity.get_random_otp(4)
            print(f"[generate_otp] Generated OTP for ACTIVE user(s): {otp}")
            response_emails = []

            for rec in active_accs:
                print(f"[generate_otp] Updating OTP for user_seq {rec.user_seq}")
                qparams = [otp, rec.user_seq]
                self.connDB.execute_prepared_stmt("sotrueappuser", "UPDATE_MOBILE_OTP", qparams)
                response_emails.append(rec.email_id)

            AppSMSSender.send_sms(
                mobile,
                f"Hey Hey! Use this One Time Password {otp} to log in to your SoTrue account. "
                f"This OTP will be valid for the next 5 mins - SOTRUE"
            )
            print(f"[generate_otp] OTP sent to mobile: {mobile}")

            response = self.create_response(dict(active_accs=response_emails), "UE097")
            print(f"[generate_otp] Returning response for active users")
            return response

        # No active account, create new one
        otp = AppSecurity.get_random_otp(4)
        print(f"[generate_otp] No active account found, creating new with OTP: {otp}")

        qparams = [
            "", None, mobile, "PENDING",
            self.utils.get_cur_timestamp(), 2000,
            AppConfigs.server_key, "NORMAL", otp
        ]
        user_seq = self.connDB.execute_prepared_stmt("sotrueappuser", "INSERT_USER_RECORD_NEW", qparams)
        print(f"[generate_otp] New user created with user_seq: {user_seq}")

        AppSMSSender.send_sms(
            mobile,
            f"Hey Hey! Use this One Time Password {otp} to log in to your SoTrue account. "
            f"This OTP will be valid for the next 5 mins - SOTRUE"
        )
        print(f"[generate_otp] OTP sent to new mobile registration: {mobile}")

        response = self.create_response(dict(active_accs=[]), "UE096")
        print(f"[generate_otp] Returning response for new user")
        return response

        

    def submit_otp_new(self):
        print("Entering submit_otp_new")
        otp = None

        if "email" in self.params:
            qparams = [self.params["mobile"], self.params["email"]]
            print("Fetching OTP with email:", qparams)
            otp = self.connDB.execute_prepared_stmt("sotrueappuser", "GET_MOBILE_OTP_EMAIL", qparams)
        else:
            qparams = [self.params["mobile"], 'PENDING']
            print("Fetching PENDING OTP:", qparams)
            otp = self.connDB.execute_prepared_stmt("sotrueappuser", "GET_MOBILE_OTP", qparams)
            if not otp:
                qparams = [self.params["mobile"], 'ACTIVE']
                print("Fetching ACTIVE OTP:", qparams)
                otp = self.connDB.execute_prepared_stmt("sotrueappuser", "GET_MOBILE_OTP", qparams)

        if otp:
            print("OTP found:", otp[0].mobile_otp)
            if otp[0].mobile_otp != str(self.params["otp"]):
                print("OTP mismatch! Provided:", self.params["otp"])
                response = self.create_error("UE004")
                print("Returning error response:", response)
                return response
            else:
                qparams = [self.params["mobile"], 'ACTIVE', 'PENDING']
                print("Fetching user data:", qparams)
                user_data = self.connDB.execute_prepared_stmt("sotrueappuser", "GET_USER_DETAILS_MOBILE", qparams)

                if "email" in self.params:
                    print("Validating against provided email:", self.params["email"])
                    for rec in user_data:
                        if rec.status == "ACTIVE" and rec.email_id != self.params["email"]:
                            qparams = ["xxxxxxxxxxxx", rec.user_seq]
                            print("Masking mobile for user_seq:", rec.user_seq)
                            self.connDB.execute_prepared_stmt("sotrueappuser", "UPDATE_USER_MOBILE", qparams)

                    qparams = [self.params["mobile"], 'ACTIVE']
                    print("Fetching user_record after masking:", qparams)
                    user_record = self.connDB.execute_prepared_stmt("sotrueappuser", "GET_USER_DETAILS_MOBILE_ACTIVE", qparams)

                    qparams = [user_record[0].last_profile]
                    print("Fetching profile data:", qparams)
                    profile_data = self.connDB.execute_prepared_stmt("sotrueappuser", "GET_PROFILE_DETAILS", qparams)

                    password = AppSecurity.get_random_password(12)
                    print("Generated password:", password)
                    qparams = [AppSecurity.hash(password), None, user_record[0].user_seq]
                    print("Updating user password:", qparams)
                    self.connDB.execute_prepared_stmt("sotrueappuser", "UPDATE_USER_PASSWORD", qparams)

                    encr_data = AppSecurity.encrypt_str(user_record[0].email_id)
                    uid = "##uid##_" + encr_data["value"].decode("utf-8")
                    key = encr_data["key"]
                    encr_data = AppSecurity.encrypt_str(password, key)
                    passwd = "##pwd##_" + encr_data["value"].decode("utf-8")

                    print("Generated uid and password:", uid, passwd)
                    qparams = [key.decode("utf-8"), uid, passwd]
                    print("Inserting session credentials:", qparams)
                    self.connDB.execute_prepared_stmt("sotrueappuser", "INSERT_SESSION_CREDS", qparams, replication=False)

                    response = dict(
                        user_seq=user_record[0].user_seq,
                        profile_seq=user_record[0].last_profile,
                        user_handle=profile_data[0].user_handle,
                        account_type=profile_data[0].type,
                        uid=uid,
                        pwd=passwd,
                        max_file_size=60
                    )
                    print("User session response object:", response)

                    session_data = {
                        "_user_seq": user_record[0].user_seq,
                        "_session_expiry": AppConfigs.session_expiry,
                        "_profile_seq": user_record[0].last_profile,
                        "_email_id": user_record[0].email_id
                    }
                    print("Starting session with:", session_data)
                    self.session.start_user_session(session_data)

                    next_step = "HOME" if user_record[0].is_personalized == "YES" else "PERSONALIZE"
                    message_code = "UE104" if user_record[0].is_personalized == "YES" else "UE007"
                    response = self.create_response(response, message_code, addnl={"next_step": next_step})
                    print("Final response being returned:", response)
                    return response
                else:
                    qparams = [otp[0].mobile_otp, user_data[0].user_seq]
                    print("Updating OTP status for user:", qparams)
                    self.connDB.execute_prepared_stmt("sotrueappuser", "UPDATE_USER_OTP", qparams)
                    self.params["user_seq"] = user_data[0].user_seq
                    print("Delegating to submit_otp with mobile_login=True")
                    return self.submit_otp(mobile_login=True)
        else:
            print("No OTP found, returning error")
            response = self.create_error("UE004")
            print("Returning error response:", response)
            return response


    def submit_otp_email(self):
        qparams = [self.params["mobile"], 'ACTIVE']
        otp = self.connDB.execute_prepared_stmt("sotrueappuser","GET_EMAIL_OTP",qparams)        
        user_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_DETAILS_MOBILE_ACTIVE",qparams)
        if otp:
            if otp[0].email_otp != str(self.params["otp"]):
                response = self.create_error("UE004")   
                return(response)
            else:
                qparams = [self.params['email'],'ACTIVE',user_data[0].user_seq]
                email_data = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_DUP_EMAIL",qparams)
                user_seq = None
                if email_data:
                    qparams = [self.params["mobile"],email_data[0].user_seq]
                    self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_USER_MOBILE",qparams)

                    qparams = ['INACTIVE',user_data[0].user_seq]
                    self.connDB.execute_prepared_stmt("sotrueappuser","DEACTIVATE_USER",qparams)

                    qparams = [user_data[0].user_seq]
                    user_profile = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_PROFILE",qparams)
                    if user_profile:
                        qparams = ['INACTIVE',user_profile[0].profile_seq]
                        self.connDB.execute_prepared_stmt("sotrueappuser","DEACTIVATE_PROFILE",qparams)
                    user_seq = email_data[0].user_seq
                else:
                    qparams = [self.params["email"],user_data[0].user_seq]
                    self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_USER_EMAIL",qparams)
                    user_seq = user_data[0].user_seq

                qparams = [None,otp[0].user_seq]
                self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_EMAIL_OTP",qparams)
                response = self.create_response(dict(),"UE101")
                return(response)
        else:
            response = self.create_error("UE004")   
            return(response)


    def generate_otp_email(self):
        qparams = [self.params["mobile"],'ACTIVE']
        user_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_DETAILS_MOBILE_ACTIVE",qparams)
        if not user_data:
            response = self.create_error("UE098")   
            return(response)
        
        otp = AppSecurity.get_random_otp(4)
        qparams = [otp,user_data[0].user_seq]
        self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_EMAIL_OTP",qparams)

        mailer = AppEmail()
        mailer.send_email(self.params["email"],None,"SoTrue Account Sign up",
                "Hey there!\n\nUse the One-Time Password (OTP) below to sign up for your SoTrue account:\n\nYour OTP: " + str(otp) + "\n\nPlease note, this OTP is valid for the next 5 minutes.\n\nBest,\nTeam SoTrue");                    

        response = self.create_response(dict(),"UE002")
        return(response)


    def verify_user_name(self):
        for x in self.params["user_name"]:
            if x == ' ':
                response = self.create_error("UE090")   
                return response

        qparams = [self.params["mobile"], 'ACTIVE']
        user_data = self.connDB.execute_prepared_stmt("sotrueappuser", "GET_USER_DETAILS_MOBILE_ACTIVE", qparams)

        self.logger.log_message("VERIFY_USER_NAME")
        self.logger.log_message(self.params)
        self.logger.log_message(qparams)
        self.logger.log_message("Fetched user_data: " + str(user_data))

        if not user_data:
            self.logger.log_message("No user found with given mobile and ACTIVE status")
            response = self.create_error("UE098")  # You can define this error
            return response

        if len(user_data) > 1:
            self.logger.log_message("⚠️ Multiple ACTIVE users found with same mobile number!")
            self.logger.log_message("User entries: " + str(user_data))

        qparams = [user_data[0].user_seq]
        user_profile = self.connDB.execute_prepared_stmt("sotrueappuser", "GET_PROFILE_FOR_USER", qparams)

        qparams = [self.params['user_name'], user_profile[0].profile_seq]
        profile_seq = self.connDB.execute_prepared_stmt("sotrueappuser", "CHECK_HANDLE_SEQ", qparams)

        if profile_seq:
            response = self.create_error("UE099")   
            return response
        else:
            qparams = [self.params["user_name"], user_profile[0].profile_seq]
            self.connDB.execute_prepared_stmt("sotrueappuser", "UPDATE_USER_HANDLE", qparams)

        response = self.create_response(dict(), "UE100")
        return response



    def register_user(self):
        print("===== Starting register_user =====")
        
        qparams = [self.params["mobile"], 'ACTIVE']
        print("Getting user details for mobile:", self.params["mobile"], " | Params:", qparams)
        
        user_details = self.connDB.execute_prepared_stmt("sotrueappuser", "GET_USER_DETAILS_MOBILE_ACTIVE", qparams)
        print("Fetched user_details:", user_details)

        if user_details:
            print("User found. user_seq:", user_details[0].user_seq, "| email_id:", user_details[0].email_id, "| last_profile:", user_details[0].last_profile)

            qparams = [self.params["full_name"], self.params["email"], user_details[0].user_seq]
            print("Updating user name with:", qparams)
            self.connDB.execute_prepared_stmt("sotrueappuser", "UPDATE_USER_NAME", qparams)

            qparams = [self.params["full_name"], self.params["user_name"], user_details[0].last_profile]
            print("Updating profile display name with:", qparams)
            self.connDB.execute_prepared_stmt("sotrueappuser", "UPDATE_PROFILE_DISPLAY_NAME", qparams)

            qparams = [user_details[0].last_profile]
            print("Fetching profile data with:", qparams)
            profile_data = self.connDB.execute_prepared_stmt("sotrueappuser", "GET_PROFILE_DETAILS", qparams)
            print("Fetched profile_data:", profile_data)

            password = AppSecurity.get_random_password(12)
            print("Generated random password:", password)

            hashed_password = AppSecurity.hash(password)
            print("Hashed password:", hashed_password)
            qparams = [hashed_password, None, user_details[0].user_seq]
            print("Updating user password with:", qparams)
            self.connDB.execute_prepared_stmt("sotrueappuser", "UPDATE_USER_PASSWORD", qparams)

            print("Preparing to encrypt user email.")
            email = user_details[0].email_id
            if email is None:
                print("WARNING: user_details[0].email_id is None, using email from params")
                email = self.params.get("email")
                if email is None:
                    print("ERROR: Both database email and params email are None!")
                    return self.create_error("UE013")

            encr_data = AppSecurity.encrypt_str(email)
            print("Encrypted email:", encr_data)

            uid = "##uid##_" + encr_data["value"].decode("utf-8")
            key = encr_data["key"]
            print("Generated uid:", uid, "| Encryption key (raw):", key)

            print("Encrypting password with same key...")
            encr_data = AppSecurity.encrypt_str(password, key)
            print("Encrypted password data:", encr_data)

            passwd = "##pwd##_" + encr_data["value"].decode("utf-8")
            print("Generated passwd:", passwd)

            qparams = [key.decode("utf-8"), uid, passwd]
            print("Inserting session credentials with:", qparams)
            self.connDB.execute_prepared_stmt("sotrueappuser", "INSERT_SESSION_CREDS", qparams, replication=False)

            response = dict(
                user_seq=user_details[0].user_seq,
                profile_seq=user_details[0].last_profile,
                user_handle=profile_data[0].user_handle,
                account_type=profile_data[0].type,
                uid=uid,
                pwd=passwd,
                max_file_size=60
            )
            print("Constructed response payload:", response)

            session_data = {
                "_user_seq": user_details[0].user_seq,
                "_session_expiry": AppConfigs.session_expiry,
                "_profile_seq": user_details[0].last_profile,
                "_email_id": user_details[0].email_id
            }
            print("Starting session with session_data:", session_data)
            self.session.start_user_session(session_data)

            print("Registration successful, returning response.")
            response = self.create_response(response, "UE003")
            print("Final response:", response)
            return response

        print("User not found with mobile:", self.params["mobile"])
        response = self.create_error("UE012")
        print("Returning error response:", response)
        return response


    def resend_otp(self):
        otp = AppSecurity.get_random_otp(4)
        if self.params["type"] == "MOBILE":
            qparams = [self.params["mobile"],'PENDING']
            user_record = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_DETAILS_MOBILE_ACTIVE",qparams)        
            if user_record:
                qparams = [otp,user_record[0].user_seq]
            else:
                qparams = [self.params["mobile"],'ACTIVE']
                user_record = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_DETAILS_MOBILE_ACTIVE",qparams)  
                if user_record:
                    qparams = [otp,user_record[0].user_seq]
            self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_MOBILE_OTP",qparams) 
            AppSMSSender.send_sms(self.params["mobile"],"Hey Hey! Use this One Time Password " + str(otp) + " to log in to your SoTrue account. This OTP will be valid for the next 5 mins - SOTRUE")
        elif self.params["type"] == "EMAIL":
            qparams = [self.params["mobile"],'ACTIVE']
            user_record = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_DETAILS_MOBILE_ACTIVE",qparams)         
            if user_record:
                qparams = [otp,user_record[0].user_seq]
            else:
                qparams = [self.params["mobile"],'PENDING']
                user_record = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_DETAILS_MOBILE_ACTIVE",qparams)  
                if user_record:
                    qparams = [otp,user_record[0].user_seq]            
            self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_EMAIL_OTP",qparams)
            mailer = AppEmail()
            mailer.send_email(self.params["email"],None,"SoTrue Account Sign up",
                "\nHey Hey! Use this One Time " + str(otp) + " to Sign up for your SoTrue account. This OTP will be valid for the next 5 minutes.\n\nYour Biggest Fans,\nTeam SoTrue");                    
        
        response = self.create_response(dict(), "UE102")
        return(response)


    def get_code_values(self):
        qparams = [self.params["code_type"],"ACTIVE"]
        result_set = self.connDB.execute_prepared_stmt("sotrueappuser","GET_CODE_VALUES",qparams)
        response = self.utils.convert_tuples_to_dicts(result_set)

        if len(response) != 0:
            response = self.create_response(response,"UE011")
            return(response)
        else:
            response = self.create_error("UE012")
            return(response)


    def send_password_otp(self):
        qparams = [self.params["email"]]
        user_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_CREDENTIALS",qparams)

        if user_data and user_data[0].is_gmail:
            response = self.create_error("UE079")
            return(response)

        if user_data:
            otp = AppSecurity.get_random_otp(4)
            self.logger.log_message("OTP for password reset is : " + str(otp))
            qparams = [otp,user_data[0].user_seq]
            self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_USER_OTP",qparams)
            
            mailer = AppEmail()
            mailer.send_email(self.params["email"],None,"SoTrue Account Password Reset",
                "Hey there!\n\nYour One-Time Password (OTP) to reset your SoTrue account password is: " + str(otp) + "\n\nBest,\nTeam SoTrue\n")
            response = self.create_response(dict(user_seq=user_data[0].user_seq),"UE018")
            return(response)
        
        response = self.create_error("UE017")
        return(response)


    def submit_password_otp(self):
        if not self.session.is_access_key_valid(self.params["_access_key"],-1):
            response = self.create_error("UE021")
            return(response)
    
        qparams = [self.params["user_seq"]]
        otp_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_OTP",qparams)
        if otp_data and otp_data[0].activation_otp == self.params["otp"]:            
            response = self.create_response(dict(),"UE020")
            return(response)
            
        response = self.create_error("UE019")
        return(response)


    def set_new_password(self):
        if not self.session.is_access_key_valid(self.params["_access_key"],-1):
            response = self.create_error("UE021")
            return(response)
        
        qparams = [self.params["user_seq"]]
        otp_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_OTP",qparams)
        if otp_data and otp_data[0].activation_otp == self.params["otp"]:                        
            qparams = [AppSecurity.hash(self.params["password"]),"",self.params["user_seq"]]
            self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_USER_PASSWORD",qparams)
            self.tracker.track_forgot_password(self.get_user(self.params["user_seq"]))
            response = self.create_response(dict(),"UE022")
            return(response)

        response = self.create_error("UE019")
        return(response)   


    def change_password(self):

        qparams = [self.params["user_id"]]
        user_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_CREDENTIALS",qparams)

        if user_data:
            if user_data[0].status != 'ACTIVE':
                response = self.create_error("UE005")
                return(response)            
            if not AppSecurity.check_hash(user_data[0].password,self.params["old_password"]):
                response = self.create_error("UE080")
                return(response)

        qparams = [AppSecurity.hash(self.params["new_password"]),"",self.session.get_session_value("_user_seq")]
        self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_USER_PASSWORD",qparams)

        qparams = [self.session.get_session_value("_user_seq")]
        email_id = self.connDB.execute_prepared_stmt("sotrueappuser","GET_EMAIL_ID",qparams)
        
        encr_data = AppSecurity.encrypt_str(email_id[0].email_id)
        uid = "##uid##_" + encr_data["value"].decode("utf-8")
        key = encr_data["key"]
        encr_data = AppSecurity.encrypt_str(self.params["new_password"],key)
        passwd = "##pwd##_" + encr_data["value"].decode("utf-8")

        qparams = [key.decode("utf-8"),uid,passwd]
        self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_SESSION_CREDS",qparams)
        self.tracker.track_change_password()
                    
        response = self.create_response(dict(uid = uid,pwd = passwd),"UE022")
        return(response)
    

    def submit_post(self):
        print("params =>")
        print(self.params)
        if self.params["uploads"] is not None:
            if self.params["post_type"] == "PAID":
                qparams = [self.session.get_session_value("_profile_seq")]
                free_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_FREE_MEDIA_COUNT",qparams)
                if free_count[0].count < 3:
                    response = self.create_error("UE091")   
                    return(response)

            qparams = [self.session.get_session_value("_profile_seq")]
            profile_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PRIVACY",qparams)
            file_path = self.configs.temp_folder_path + self.params["uploads"][0]["upload_name"]
            file_name = self.params["uploads"][0]["upload_name"]
            target_path = SotrueAppConfig.media_path + str(self.session.get_session_value("_profile_seq"))
            blur_path = None
            if not self.utils.folder_exists(target_path):
                self.utils.create_folder(target_path)
            target_path += "/" + self.params["uploads"][0]["upload_name"]             
            if self.params["media_type"] == "IMAGE":
                if profile_data[0].enable_watermark == "YES":
                    self.utils.watermark_image(file_path,SotrueAppConfig.watermark_file,target_path)
                    if AppConfigs.s3_enabled:
                        file_name = self.utils.get_uuid_str() + self.params["uploads"][0]["upload_name"][self.params["uploads"][0]["upload_name"].rfind("."):]
                        awsS3 = AppAwsS3()
                        awsS3.send_media_file(target_path, file_name, AppConfigs.s3_posts_folder)
                else:
                    resize_extn = self.utils.resize_image(file_path)
                    file_path =file_path [0:file_path.rfind(".")+1] + resize_extn
                    target_path =target_path [0:target_path.rfind(".")+1] + resize_extn
                    self.utils.copy_files(file_path,target_path) 
                    if AppConfigs.s3_enabled:
                        file_name = self.utils.get_uuid_str() + self.params["uploads"][0]["upload_name"][self.params["uploads"][0]["upload_name"].rfind("."):]
                        awsS3 = AppAwsS3()
                        awsS3.send_media_file(file_path, file_name, AppConfigs.s3_posts_folder)
                    
                if not AppConfigs.s3_enabled:
                    self.cache_uploaded_file(self.params["uploads"][0]["key"],target_path)

                blur_path = target_path[0:target_path.rfind(".")] + "_blur" + ".jpg"
                self.utils.blur_image(file_path,blur_path)                
                if AppConfigs.s3_enabled:
                    blur_file_name = self.utils.get_uuid_str() + ".jpg"
                    awsS3 = AppAwsS3()
                    awsS3.send_media_file(blur_path, blur_file_name, AppConfigs.s3_posts_folder)
                    self.utils.delete_file(blur_path)
                    blur_path = "/" + blur_file_name
                else:
                    self.cache_uploaded_file(self.params["uploads"][0]["key"] + "_blur",blur_path)
            else:
                self.utils.validate_video_file(file_path)
                self.utils.copy_files(file_path,target_path)
                if AppConfigs.s3_enabled:
                    file_name = self.utils.get_uuid_str() + self.params["uploads"][0]["upload_name"][self.params["uploads"][0]["upload_name"].rfind("."):]
                    awsS3 = AppAwsS3()
                    awsS3.send_media_file(file_path, file_name, AppConfigs.s3_posts_folder)
                else:                    
                    self.cache_uploaded_file(self.params["uploads"][0]["key"],target_path)            
                    self.utils.delete_file(file_path)

            frame_path = None            
            duration = 0
            if self.params["media_type"] == "VIDEO":                
                frame_path = target_path[0:target_path.rfind(".")] + "_frame" + ".jpg"
                print("is_playlist",self.params["is_playlist"])
                # Use episode thumbnail for playlist videos
                if self.params["is_playlist"] == "YES" and self.params.get("episode_seq"):
                    qparams = [self.params["episode_seq"]]
                    episode_details = self.connDB.execute_prepared_stmt("sotrueappplay", "GET_EPISODE_DETAILS", qparams)
                    print("episode_details",episode_details)
                    if episode_details and episode_details[0].thumb_file:
                        frame_path = episode_details[0].thumb_file
                
                # Only generate frame if we don't have an episode thumbnail or if frame extraction fails
                if not frame_path or not self.utils.get_first_video_frame(target_path,frame_path):
                    
                    if AppConfigs.s3_enabled:
                        frame_file_name = self.utils.get_uuid_str() + ".jpg"
                        awsS3 = AppAwsS3()
                        awsS3.send_media_file(SotrueAppConfig.default_video_image, frame_file_name, AppConfigs.s3_posts_folder)
                        frame_path = "/" + frame_file_name
                    else:
                        self.utils.copy_files(SotrueAppConfig.default_video_image,frame_path)
                        self.cache_uploaded_file(self.params["uploads"][0]["key"] + "_frame",frame_path)
                else:
                    blur_path = target_path[0:target_path.rfind(".")] + "_blur" + ".jpg"
                    self.utils.blur_image(frame_path,blur_path)
                    if AppConfigs.s3_enabled:
                        frame_file_name = self.utils.get_uuid_str() + ".jpg"
                        awsS3 = AppAwsS3()
                        awsS3.send_media_file(frame_path, frame_file_name, AppConfigs.s3_posts_folder)
                        self.utils.delete_file(frame_path)
                        frame_path = "/" + frame_file_name

                        blur_file_name = self.utils.get_uuid_str() + ".jpg"
                        awsS3.send_media_file(blur_path, blur_file_name, AppConfigs.s3_posts_folder)
                        self.utils.delete_file(blur_path)
                        blur_path = "/" + blur_file_name
                    else:
                        self.cache_uploaded_file(self.params["uploads"][0]["key"] + "_frame",frame_path)                        
                        self.cache_uploaded_file(self.params["uploads"][0]["key"] + "_blur",blur_path)
                
                if self.params["is_playlist"] == "YES" and self.params.get("episode_seq"):
                    qparams = [self.params["episode_seq"]]
                    episode_details = self.connDB.execute_prepared_stmt("sotrueappplay", "GET_EPISODE_DETAILS", qparams)
                    print("episode_details",episode_details)
                    if episode_details and episode_details[0].thumb_file:
                        frame_path = episode_details[0].thumb_file

                duration = self.utils.get_video_duration(target_path)

            qparams = [self.session.get_session_value("_profile_seq"),
                       self.params["comments"].replace("\n","<br>") if "comments" in self.params else "",self.utils.get_cur_timestamp(),
                       self.params["post_type"],
                       0 if "viewer_fee" not in self.params else int(self.params["viewer_fee"])*100,
                       '9999-12-31' if "expire_on" not in self.params else self.params["expire_on"],
                       "ACTIVE",
                       self.params["is_playlist"] if "is_playlist" in self.params else 'NO',
                       self.params["episode_seq"] if "episode_seq" in self.params else None,
                       self.params["location"] if "location" in self.params else None,
                       self.params["schedule"] if "schedule" in self.params else None,]

            post_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_POST",qparams)
            self.tracker.track_post()

            # if "is_playlist" in self.params and self.params["is_playlist"] == "YES":
            #     if "episode_seq" in self.params:
            #         qparams = ['PENDING',None,None,None,self.params["episode_seq"]]
            #         self.connDB.execute_prepared_stmt("sotrueappplay","RESET_EPISODE_APPROVAL",qparams)

            qparams = [post_seq,file_name,
                       self.utils.get_file_size(target_path),self.params["media_type"],"ACTIVE",
                       frame_path if not frame_path else frame_path[frame_path.rfind("/")+1:],
                       self.params["file_format"], duration, 
                       blur_path if not blur_path else blur_path[blur_path.rfind("/")+1:],
                       "YES" if AppConfigs.s3_enabled else "NO"]
            media_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_POST_MEDIA",qparams)

            if "topics" in self.params:
                topics = self.utils.parse_json(self.params["topics"])
                for topic in topics:
                    qparams = [post_seq,topic]
                    post_topic_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_POST_TOPIC",qparams)

            if "reactions" in self.params:
                reactions = self.utils.parse_json(self.params["reactions"])
                for reaction in reactions:
                    qparams = [post_seq,reaction]
                    post_reaction_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_POST_REACTION",qparams)

            comment_tags = None
            if "comments" in self.params:
                s = [self.params["comments"]]
                comment_tags = self.validate_user_handles(s)

            if "tagged_users" in self.params:
                user_list = self.utils.parse_json(self.params["tagged_users"])                
                for user in user_list:
                    qparams = [self.session.get_session_value("_profile_seq"), user["profile_seq"], 
                               self.utils.get_cur_timestamp(), post_seq, "POST"]
                    tag_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_POST_TAG",qparams)

                    qparams = [user["profile_seq"]]
                    profile_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)
                    qparams = [self.session.get_session_value("_profile_seq")]
                    self_profile = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)

                    if profile_details[0].user_handle in comment_tags:
                        comment_tags.remove(profile_details[0].user_handle)

                    if profile_details[0].fcm_key:
                        payload = {"type":"POST_TAG","post_seq":str(post_seq)}
                        fcm = AppFirebase()
                        fcm.send_fcm_notification(profile_details[0].fcm_key,
                                                            "Post Tag Alert",
                                                            "Hey! Hey! " + self_profile[0].display_name + " has tagged you in a post!",
                                                        payload)

            self.insert_tags(self.params["comments"] if "comments" in self.params else "","POST_CAPTION",
                            post_seq, comment_tags)

            if AppConfigs.s3_enabled:
                self.utils.delete_file(target_path)

            response = self.create_response(dict(post_seq=post_seq),"UE010")
            return(response)
            
        else:
            response = self.create_error("UE009")   
            return(response)
        

    def delete_post(self):
        qparams = ["INACTIVE",self.params["post_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_POST_STATUS",qparams)
        self.tracker.track_delete_post()

        response = self.create_response(dict(),"UE014")
        return(response)


    def get_posts_user(self):        
        qparams = [self.params['req_profile_seq'],self.utils.get_cur_timestamp(),self.session.get_session_value("_profile_seq")]
        subs = None
        if str(self.params['req_profile_seq']) != str(self.session.get_session_value("_profile_seq")):
            subs = {"<STATUS_LIST>":"'ACTIVE'"}
        else:
            subs = {"<STATUS_LIST>":"'ACTIVE'"}
        
        if self.params["media_type"] == "ALL":
            subs["<MEDIA_TYPE_LIST>"] = "'IMAGE','VIDEO'"
        else:
            subs["<MEDIA_TYPE_LIST>"] = "'" + self.params["media_type"] + "'"
            
        post_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_POSTS",qparams,subs=subs,limit=self.is_paginated())                
                    
        if post_data:
            post_seqs = []
            profile_seqs = []
            for row in post_data:
                post_seqs.append(row.post_seq)  
                profile_seqs.append(row.profile_seq)

            if "first_seq" in self.params:
                if self.params["first_seq"] in post_seqs:
                    rec_count = 0;
                    for rec in post_data:
                        if rec.post_seq == self.params["first_seq"]:
                            post_data.pop(rec_count)
                            post_data.insert(0,rec)
                            break;
                        rec_count += 1
                else:
                    params = [self.params["first_seq"]]
                    post_data_sp = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_POSTS_SPECIAL",params)
                    post_data.pop()
                    post_data.insert(0,post_data_sp[0])

            post_data_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_POSTS_COUNT",qparams,subs=subs)            
            post_data = self.utils.convert_tuples_to_dicts(post_data)           

            subs = {"<POST_LIST>":self.utils.convert_to_delim_str(post_seqs,",")}
                        
            qparams = [self.params['req_profile_seq']]
            profile_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)

            is_profile_subscribed = True
            if profile_data[0].type == "PAID" and self.params['req_profile_seq'] != self.session.get_session_value("_profile_seq"):
                today = self.utils.get_today_date_str()
                qparams = [self.session.get_session_value("_profile_seq"),self.params['req_profile_seq'],today,today]
                subscription = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_PROFILE_SUBSCRIPION",qparams)
                is_profile_subscribed = False if not subscription else True
            
            qparams = [self.session.get_session_value("_profile_seq")]
            subscribed_posts = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_POST_SUBSCRIPION_LIST",qparams,subs=subs)            
            post_views = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_POST_VIEWS",qparams,subs=subs)
            post_bookmarks = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_BOOKMARKS",qparams,subs=subs)
            post_comments = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_POST_COMMENTS",qparams,subs=subs)
            post_tags = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_TAG_LIST",None,subs=subs)
            post_reactions = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_REACTIONS_LIST",None,subs=subs)            
            query_params = qparams + ["LIKE"]
            post_likes = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_POST_LIKES",query_params,subs=subs)                                    

            post_likes_indexed = {}
            for rec in post_likes:
                post_likes_indexed[rec.post_seq] = rec.like_seq             

            post_reactions_indexed = {}
            for rec in post_reactions:
                if rec.post_seq not in post_reactions_indexed:
                    post_reactions_indexed[rec.post_seq] = []
                post_reactions_indexed[rec.post_seq].append(rec.reaction)

            user_reactions_indexed = {}
            for react in self.reactions:
                query_params = [self.session.get_session_value("_profile_seq"),react]
                post_reactions = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_POST_LIKES",query_params,subs=subs)                                
                for rec in post_reactions:
                    if rec.post_seq not in user_reactions_indexed:
                        user_reactions_indexed[rec.post_seq] = {}
                    if react not in user_reactions_indexed[rec.post_seq]:
                        user_reactions_indexed[rec.post_seq][react] = {"selected":"NO","count":0}                    
                    user_reactions_indexed[rec.post_seq][react]["selected"] = "YES"  

                query_params = [react]
                reaction_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_LIKE_COUNTS",query_params,subs=subs)
                for rec in reaction_counts:
                    if rec.post_seq not in user_reactions_indexed:
                        user_reactions_indexed[rec.post_seq] = {}
                    if react not in user_reactions_indexed[rec.post_seq]:
                        user_reactions_indexed[rec.post_seq][react] = {"selected":"NO","count":0}
                    user_reactions_indexed[rec.post_seq][react]["count"] = rec.count
            

            post_views_indexed = {}
            for rec in post_views:
                post_views_indexed[rec.post_seq] = rec.view_seq 
            post_comments_indexed = {}
            for rec in post_comments:
                post_comments_indexed[rec.content_seq] = rec.comment_seq
            post_bookmark_indexed = {}
            for rec in post_bookmarks:
                post_bookmark_indexed[rec.post_seq] = rec.bookmark_seq
            subscribed_posts_indexed = {}
            for rec in subscribed_posts:
                subscribed_posts_indexed[rec.subscribed_post_seq] = rec.subscription_seq
            post_tags_indexed = {}
            for rec in post_tags:
                post_tags_indexed[rec.post_seq] = rec.post_seq            

            query_params = ["LIKE"]
            like_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_LIKE_COUNTS",query_params,subs=subs)
            
            view_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_VIEWS_COUNTS",None,subs=subs)
            qparams = ["POST"]
            comment_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_COMMENT_COUNTS",qparams,subs=subs)

            like_counts_indexed = dict()            
            view_counts_indexed = dict()
            comment_counts_indexed = dict()
            for like in like_counts:
                like_counts_indexed[like.post_seq] = like.count            
            for view in view_counts:
                view_counts_indexed[view.post_seq] = view.count
            for comment in comment_counts:
                comment_counts_indexed[comment.content_seq] = comment.count          

            profile_subs = {"<PROFILE_LIST>":self.utils.convert_to_delim_str(profile_seqs,",")}
            gstn_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_GSTIN_LIST",None,subs=profile_subs)
            gstn_data_indexed = {}
            for rec in gstn_data:
                if rec.gstin:
                    gstn_data_indexed[rec.profile_seq] = rec.gstin

            generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)            
            for row in post_data:                
                row["likes"] = 0 if row["post_seq"] not in like_counts_indexed else like_counts_indexed[row["post_seq"]]                
                row["views"] = 0 if row["post_seq"] not in view_counts_indexed else view_counts_indexed[row["post_seq"]]
                row["comments"] = 0 if row["post_seq"] not in comment_counts_indexed else comment_counts_indexed[row["post_seq"]]                
                row["is_liked"] = 'YES' if row["post_seq"] in post_likes_indexed else 'NO'
                row["user_reactions"] = {} if  row["post_seq"] not in user_reactions_indexed else user_reactions_indexed[row["post_seq"]]
                row["is_viewed"] = 'YES' if row["post_seq"] in post_views_indexed else 'NO'
                row["is_bookmarked"] = 'YES' if row["post_seq"] in post_bookmark_indexed else 'NO'
                row["is_commented"] = 'YES' if row["post_seq"] in post_comments_indexed else 'NO'
                row["posted_on"] = self.utils.get_formatted_time_past(row["posted_on"])
                row["is_subscribed"] = 'YES' if row["post_seq"] in subscribed_posts_indexed or self.params['req_profile_seq']== self.session.get_session_value("_profile_seq") else 'NO'                
                if row["profile_seq"] not in gstn_data_indexed:                    
                    row["viewer_fee_display"] = self.utils.format_currency(int(row["viewer_fee"]/100))
                else:                    
                    row["viewer_fee_display"] = self.utils.format_currency(round((row["viewer_fee"] + (row["viewer_fee"]*0.18))/100,2))
                row["viewer_fee"] = int(row["viewer_fee"]/100)
                row["profile_type"] = profile_data[0].type
                row["is_profile_subscribed"] = "YES" if is_profile_subscribed else "NO"
                row["is_tagged"] = "YES" if row["post_seq"] in post_tags_indexed else "NO"      
                
                if profile_data[0].paid_account_fee:
                    if profile_data[0].gstin:
                        row["profile_fee_display"]=self.utils.format_currency(round((profile_data[0].paid_account_fee + (profile_data[0].paid_account_fee*0.18))/100,2)) 
                    else:
                        row["profile_fee_display"]=self.utils.format_currency(int(profile_data[0].paid_account_fee/100)) 
                else:
                   row["profile_fee_display"]=0

                row["profile_fee"] = int(profile_data[0].paid_account_fee)/100 if profile_data[0].paid_account_fee else 0
                if str(self.params['req_profile_seq']) == str(self.session.get_session_value("_profile_seq")):
                    row["media_file"] = generic_code.create_media_url(SotrueAppConfig.media_path + str(self.params["req_profile_seq"]) + "/" + row["media_file"],AppConfigs.s3_posts_folder,row["s3_enabled"])
                    row["media_cover"] = row["media_cover"] if not row["media_cover"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(self.params["req_profile_seq"]) + "/" + row["media_cover"],AppConfigs.s3_posts_folder,row["s3_enabled"])
                    row["is_displayed"] = "YES"
                    row["fuzzy_image"] = ""
                elif not is_profile_subscribed or (row["post_type"] == "PAID" and row["is_subscribed"] != 'YES'):
                    row["media_file"] = ""
                    row["media_cover"]=""
                    row["fuzzy_image"] = row["fuzzy_image"] if not row["fuzzy_image"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(self.params["req_profile_seq"]) + "/" + row["fuzzy_image"],AppConfigs.s3_posts_folder,row["s3_enabled"])
                    row["is_displayed"] = "NO"
                else:
                    row["media_file"] = generic_code.create_media_url(SotrueAppConfig.media_path + str(self.params["req_profile_seq"]) + "/" + row["media_file"],AppConfigs.s3_posts_folder,row["s3_enabled"])
                    row["media_cover"] = row["media_cover"] if not row["media_cover"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(self.params["req_profile_seq"]) + "/" + row["media_cover"],AppConfigs.s3_posts_folder,row["s3_enabled"])
                    row["is_displayed"] = "YES"
                    row["fuzzy_image"] = ""
                if row["media_type"] == "VIDEO" and row["media_file"] and row["video_duration"] == 0:
                    duration = self.utils.get_video_duration(SotrueAppConfig.media_path + str(self.params["req_profile_seq"]) + "/" + row["media_file"])
                    row["video_duration"] = duration
                    qparams = [duration,row["content_seq"]]
                    self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_VIDEO_DURATION_POST",qparams)
                if row["post_comments"]:
                    s= [row["post_comments"]]
                    row["post_caption_tags"] = self.validate_user_handles(s)
                    row["post_comments"] = s[0]
                else:
                    row["post_caption_tags"] = []
                row["reactions"] = post_reactions_indexed[row["post_seq"]] if row["post_seq"] in post_reactions_indexed else [] 
                    
            qparams = [self.utils.get_ts_back_by(self.utils.get_cur_timestamp(),24),self.params['req_profile_seq'],
                       self.session.get_session_value("_profile_seq")]
            story_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_STORY_USER_COUNT",qparams)

            qparams = [self.params['req_profile_seq'],self.session.get_session_value("_profile_seq"),'RESTRICTED','ACTIVE']
            restricted_data = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_RESTRICTED_PROFILE",qparams)

            profile_picture = profile_data[0].profile_picture if not profile_data[0].profile_picture else generic_code.create_media_url(SotrueAppConfig.profile_path + profile_data[0].profile_picture,AppConfigs.s3_profiles_folder,profile_data[0].s3_enabled)
            cover_image = profile_data[0].cover_image if not profile_data[0].cover_image else generic_code.create_media_url(SotrueAppConfig.profile_path + profile_data[0].cover_image,AppConfigs.s3_profiles_folder,profile_data[0].s3_enabled)
            response = self.create_response(post_data,"UE011",addnl={"user_handle":profile_data[0].user_handle,
                                                                     "profile_picture":profile_picture,
                                                                     "cover_image":cover_image,
                                                                     "display_name":profile_data[0].display_name,
                                                                     "is_verified":profile_data[0].is_verified,
                                                                     "enable_comment":profile_data[0].enable_comment, 
                                                                     "enable_watermark":profile_data[0].enable_watermark,
                                                                     "story_count":story_count[0].count,
                                                                     "_total_rows":post_data_count[0].count,
                                                                     "is_restricted":"NO" if not restricted_data else "YES"})
            return(response)
        
        if self.params['req_profile_seq'] == self.session.get_session_value("_profile_seq"):
            response = self.create_error("UE064")
        else:
            response = self.create_error("UE065")
        return(response)


    def submit_post_like(self):
        qparams = [self.params['post_seq'],self.session.get_session_value("_profile_seq"),
                   self.params["type"] if "type" in self.params else "LIKE"]
        like_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_LIKE_PROFILE",qparams)
        if like_data:
            response = self.create_error("UE016")   
            return(response)
        
        qparams = [self.params['post_seq'],self.session.get_session_value("_profile_seq"), 
                   self.utils.get_cur_timestamp(),
                   self.params["type"] if "type" in self.params else "LIKE"]
        like_seq = self.connDB.execute_prepared_stmt("sotrueappuser","SAVE_POST_LIKE",qparams)
        self.tracker.track_like_post(self.get_post_user(self.params['post_seq']))

        qparams = [self.params['post_seq']]
        like_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_COUNT_POST_LIKE",qparams)
        post_profile = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_PROFILE",qparams)
        
        qparams = [post_profile[0].profile_seq]
        profile_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)
        qparams = [self.session.get_session_value("_profile_seq")]
        self_profile = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)

        if profile_details[0].fcm_key and post_profile[0].profile_seq != self.session.get_session_value("_profile_seq"):
            payload = {"type":"POST_LIKE","post_seq":str(self.params['post_seq'])}
            fcm = AppFirebase()
            fcm.send_fcm_notification(profile_details[0].fcm_key,
                                                "Post interaction alert",
                                                self_profile[0].display_name + " has interacted with your post!",
                                            payload)

        response = self.create_response(dict(like_seq=like_seq,like_count=like_count[0].count),"UE013")
        return(response)


    def remove_post_like(self):
        qparams = [self.session.get_session_value("_profile_seq"), self.params['post_seq'],
                   self.params["type"] if "type" in self.params else "LIKE"]
        self.connDB.execute_prepared_stmt("sotrueappuser","DELETE_POST_LIKE",qparams)
        self.tracker.track_delete_post_like(self.get_post_user(self.params['post_seq']))

        qparams = [self.params['post_seq']]
        like_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_COUNT_POST_LIKE",qparams)
        
        response = self.create_response(dict(like_count=like_count[0].count),"UE014")
        return(response)


    def submit_post_comment(self):
        qparams = [self.params['post_seq'], 'POST', self.params['comment'], self.session.get_session_value("_profile_seq"),
                   self.utils.get_cur_timestamp(),'ACTIVE']
        comment_seq = self.connDB.execute_prepared_stmt("sotrueappuser","SAVE_COMMENT",qparams)
        self.tracker.track_comment_post(self.get_post_user(self.params['post_seq']))

        qparams = [self.params['post_seq'], 'POST']
        comment_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_COUNT_POST_COMMENT",qparams)

        qparams = [self.params['post_seq']]
        post_profile = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_PROFILE",qparams)
        
        qparams = [post_profile[0].profile_seq]
        profile_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)
        qparams = [self.session.get_session_value("_profile_seq")]
        self_profile = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)

        if profile_details[0].fcm_key and post_profile[0].profile_seq != self.session.get_session_value("_profile_seq"):
            payload = {"type":"POST_COMMENT","post_seq":str(self.params['post_seq'])}
            fcm = AppFirebase()
            fcm.send_fcm_notification(profile_details[0].fcm_key,
                                                "Post Comment Alert",
                                                "Hey! Hey! " + self_profile[0].display_name + " has commented on your post!",
                                            payload)
        response = self.create_response(dict(comment_seq=comment_seq,comment_count=comment_count[0].count),"UE095")
        return(response)


    def remove_post_comment(self):
        qparams =[self.params['comment_seq']]
        self.connDB.execute_prepared_stmt("sotrueappuser","DELETE_COMMENT",qparams)
        self.tracker.track_remove_comment_post(self.get_post_user(self.params['post_seq']))

        qparams = [self.params['post_seq'], 'POST']
        comment_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_COUNT_POST_COMMENT",qparams)
        
        response = self.create_response(dict(comment_count=comment_count[0].count),"UE014")
        return(response)


    def submit_comment_like(self):
        qparams = [self.params['comment_seq'],self.session.get_session_value("_profile_seq")]
        like_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_COMMENT_LIKE_PROFILE",qparams)
        if like_data:
            response = self.create_error("UE016")   
            return(response)
        
        qparams = [self.params['comment_seq'], self.params['post_seq'],self.session.get_session_value("_profile_seq"),
                   self.utils.get_cur_timestamp()]
        like_seq = self.connDB.execute_prepared_stmt("sotrueappuser","SAVE_COMMENT_LIKE",qparams)
        self.tracker.track_post_comment_like(self.get_post_user(self.params['post_seq']))

        response = self.create_response(dict(like_seq=like_seq),"UE013")
        return(response)


    def remove_comment_like(self):
        qparams = [self.session.get_session_value("_profile_seq"), self.params['comment_seq'],]
        self.connDB.execute_prepared_stmt("sotrueappuser","DELETE_COMMENT_LIKE",qparams)
        self.tracker.track_delete_post_comment_like(self.get_post_user(self.params['post_seq']))

        response = self.create_response(dict(),"UE014")
        return(response)


    def get_post_comments(self):
        qparams = [self.params['post_seq'],"POST"]
        comment_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_COMMENTS",qparams,limit=self.is_paginated())

        if comment_data:
            comment_data_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_COMMENTS_COUNT",qparams)
            comment_seqs = []
            for comment in comment_data:
                comment_seqs.append(comment.comment_seq)

            subs = {"<COMMENT_LIST>":self.utils.convert_to_delim_str(comment_seqs,",")}
            qparams = ["COMMENT"]
            sub_comment_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_COMMENT_COMMENTS",qparams,subs=subs)

            qparams = [self.session.get_session_value("_profile_seq")]
            comment_like_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_COMMENT_BY_PROFILE",qparams,subs=subs)

            sub_comment_indexed = {}
            sub_comment_seqs = []
            for sub_comment in sub_comment_data:
                if sub_comment.content_seq not in sub_comment_indexed:
                    sub_comment_indexed[sub_comment.content_seq] = []
                sub_comment_indexed[sub_comment.content_seq].append(sub_comment)
                sub_comment_seqs.append(sub_comment.comment_seq)
                        
            like_data_indexed = []
            for like in comment_like_data:
                like_data_indexed.append(like.comment_seq)

            sub_like_data_indexed = []
            if sub_comment_seqs:
                subs = {"<COMMENT_LIST>":self.utils.convert_to_delim_str(sub_comment_seqs,",")}
                qparams = [self.session.get_session_value("_profile_seq")]
                sub_comment_like_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_COMMENT_BY_PROFILE",qparams,subs=subs)

                for like in sub_comment_like_data:
                    sub_like_data_indexed.append(like.comment_seq)

            comment_data = self.utils.convert_tuples_to_dicts(comment_data)
            generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger) 
            for comment in comment_data:
                comment["sub_comment_list"] = []
                comment["comment_time"] = self.utils.get_formatted_time_past(comment["comment_time"])
                comment["self_comment"] = "YES" if comment['comment_profile_seq'] == self.session.get_session_value("_profile_seq") else "NO"
                comment["self_like"] = "YES" if comment["comment_seq"] in like_data_indexed else "NO"                
                comment["profile_picture"] = comment["profile_picture"] if not comment["profile_picture"] else generic_code.create_media_url(SotrueAppConfig.profile_path + comment["profile_picture"],AppConfigs.s3_profiles_folder, comment["s3_enabled"])
                if comment["comment_seq"] in sub_comment_indexed:                    
                    for sub_comment in sub_comment_indexed[comment["comment_seq"]]:
                        sub_row = {"sub_comment_seq": sub_comment.comment_seq,
                                   "sub_comment":sub_comment.comment,
                                   "sub_comment_time":self.utils.get_formatted_time_past(sub_comment.comment_time),                        
                                   "sub_comment_profile_seq":sub_comment.comment_profile_seq,
                                   "sub_comment_user_handle":sub_comment.user_handle,
                                   "sub_comment_profile_pic":sub_comment.profile_picture if not sub_comment.profile_picture else generic_code.create_media_url(SotrueAppConfig.profile_path + sub_comment.profile_picture,AppConfigs.s3_profiles_folder, sub_comment.s3_enabled),
                                   "sub_comment_full_name":sub_comment.full_name,
                                   "self_sub_comment": "YES" if sub_comment.comment_profile_seq == self.session.get_session_value("_profile_seq") else "NO",
                                   "self_sub_like": "YES" if sub_comment.comment_seq in sub_like_data_indexed else "NO",
                                   "sub_is_verified":sub_comment.is_verified}
                        comment["sub_comment_list"].append(sub_row)
            response = self.create_response(comment_data,"UE011",addnl={"_total_rows":comment_data_count[0].count})
            return(response)
            
        response = self.create_error("UE059")
        return(response)


    def submit_comment_comment(self):
        qparams = [self.params['comment_seq'], 'COMMENT', self.params['comment'], self.session.get_session_value("_profile_seq"),
                   self.utils.get_cur_timestamp(),'ACTIVE']
        comment_seq = self.connDB.execute_prepared_stmt("sotrueappuser","SAVE_COMMENT",qparams)
        self.tracker.track_reply_comment(self.get_post_user(self.params['post_seq']))        

        response = self.create_response(dict(comment_seq=comment_seq),"UE095")
        return(response)


    def remove_comment_comment(self):
        qparams =[self.params['comment_seq']]
        self.connDB.execute_prepared_stmt("sotrueappuser","DELETE_COMMENT",qparams)
        self.tracker.remove_reply_comment(self.get_post_user(self.params['post_seq']))

        response = self.create_response(dict(),"UE014")
        return(response)


    def save_user_profile(self):
        for x in self.params["user_name"]:
            if(x == ' ') :
                response = self.create_error("UE090")   
                return(response)
        qparams = [self.params['user_name'],self.params["profile_seq"]]
        dup_check = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_HANDLE_SEQ",qparams)
        if dup_check:
            response = self.create_error("UE099")   
            return(response)

        profile_file = None
        cover_file = None
        if self.params["uploads"] is not None:
            if self.params["uploads"][0]["key"] == "profile_file":
                profile_file = self.params["uploads"][0]["upload_name"]
            elif self.params["uploads"][0]["key"] == "cover_file":
                cover_file = self.params["uploads"][0]["upload_name"]

            if len(self.params["uploads"]) > 1:
                if self.params["uploads"][1]["key"] == "profile_file":
                    profile_file = self.params["uploads"][1]["upload_name"]
                elif self.params["uploads"][1]["key"] == "cover_file":
                    cover_file = self.params["uploads"][1]["upload_name"]

            if profile_file: 
                resize_extn = self.utils.resize_image(self.configs.temp_folder_path + profile_file)
                profile_file =profile_file [0:profile_file.rfind(".")+1] + resize_extn                    
                if AppConfigs.s3_enabled:
                        file_name = self.utils.get_uuid_str() + profile_file[profile_file.rfind("."):]
                        awsS3 = AppAwsS3()
                        awsS3.send_media_file(self.configs.temp_folder_path + profile_file, file_name, AppConfigs.s3_profiles_folder)
                        profile_file = file_name
                else:
                    self.utils.copy_files(self.configs.temp_folder_path + profile_file,SotrueAppConfig.profile_path + profile_file)                                
                    self.cache_uploaded_file("profile_file",SotrueAppConfig.profile_path + profile_file)
                self.utils.delete_file(self.configs.temp_folder_path + profile_file)

            if cover_file:
                resize_extn = self.utils.resize_image(self.configs.temp_folder_path + cover_file)
                cover_file = cover_file[0:cover_file.rfind(".")+1] + resize_extn
                if AppConfigs.s3_enabled:
                        file_name = self.utils.get_uuid_str() + cover_file[cover_file.rfind("."):]
                        awsS3 = AppAwsS3()
                        awsS3.send_media_file(self.configs.temp_folder_path + cover_file, file_name, AppConfigs.s3_profiles_folder)
                        cover_file= file_name
                else:
                    self.utils.copy_files(self.configs.temp_folder_path + cover_file,SotrueAppConfig.profile_path + cover_file)                    
                    self.cache_uploaded_file("cover_file",SotrueAppConfig.profile_path + cover_file)
                self.utils.delete_file(self.configs.temp_folder_path + cover_file)

        qparams = [self.session.get_session_value("_profile_seq")]
        user_profile = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)
        if not profile_file and "reset_profile" not in self.params:
            profile_file = user_profile[0].profile_picture
        if not cover_file and "reset_cover" not in self.params:
            cover_file = user_profile[0].cover_image

        fb_link = user_profile[0].fb_link
        if 'fb_link' in self.params:
            fb_link = self.params['fb_link']
        twiter_link = user_profile[0].twiter_link
        if 'twiter_link' in self.params:
            twiter_link = self.params['twiter_link'] 
        insta_link = user_profile[0].insta_link
        if 'insta_link' in self.params:
            insta_link = self.params['insta_link']
            
        qparams = [self.params['user_name'],self.params['display_name'],
                   "" if not "user_bio" in self.params else self.params['user_bio'].replace("\n","<br>"),
                   self.params['disp_category'],profile_file, cover_file,fb_link, twiter_link, insta_link,
                   "YES" if AppConfigs.s3_enabled else "NO",
                   self.params["gender"] if "gender" in self.params else None,
                   self.session.get_session_value("_profile_seq")]        
        self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_PROFILE_DETAILS",qparams)
        self.tracker.track_update_profile()

        #qparams = [
        #    self.params['country'] if "country" in self.params else None,
        #    self.params["state"] if "state" in self.params else None,
        #    self.session.get_session_value("_user_seq")]
        #self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_USER_LOCATION",qparams)

        qparams = [self.session.get_session_value("_profile_seq")]
        self.connDB.execute_prepared_stmt("sotrueappuser","DELETE_PROFILE_CATEGORIES",qparams)
        categories = self.params["category"].split(",")
        for cat in categories:
            qparams = [self.session.get_session_value("_profile_seq"),cat]
            self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_PROFILE_CATEGORY",qparams)

        self.insert_tags(self.params["user_bio"] if "user_bio" in self.params else "","PROFILE_BIO",
                    self.session.get_session_value("_profile_seq"))         
        
        if user_profile[0].user_handle != self.params["user_name"]:
            qparams = [self.session.get_session_value("_profile_seq"),self.params["user_name"],
                       user_profile[0].user_handle]
            self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_HANDLE_HISTORY",qparams)

        response = self.create_response(dict(),"UE013")
        return(response)


    def get_user_profile(self):
        print("Entering get_user_profile")
        print(f"Initial params: {self.params}")

        if "user_handle" in self.params:
            print(f"User handle provided: {self.params['user_handle']}")
            qparams = [self.params['user_handle']]
            profile_seq_data = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_HANDLE",qparams)
            print(f"Result of CHECK_HANDLE: {profile_seq_data}")
            if profile_seq_data:
                self.params['req_profile_seq'] = profile_seq_data[0].profile_seq
                print(f"req_profile_seq set to: {self.params['req_profile_seq']}")
            else:
                response = self.create_error("UE012")
                print(f"Returning error: {response}")
                return(response)

        qparams = [self.params['req_profile_seq']]
        user_profile = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)
        print(f"User profile fetched: {user_profile}")

        if user_profile:
            verification_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_VERIFICATION",qparams)
            print(f"Verification details: {verification_details}")

            qparams = [self.session.get_session_value("_profile_seq"),self.params['req_profile_seq'],'ACTIVE']
            resctricted_data = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_RESTRICTED_PROFILE_TYPE",qparams)
            print(f"Restricted data: {resctricted_data}")

            qparams = [self.params['req_profile_seq'],self.session.get_session_value("_profile_seq"),'ACTIVE']
            resctricted_data_self = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_RESTRICTED_PROFILE_TYPE",qparams)
            print(f"Restricted data self: {resctricted_data_self}")

            qparams = [self.utils.get_ts_back_by(self.utils.get_cur_timestamp(),24),self.params['req_profile_seq'],
                       self.session.get_session_value("_profile_seq")]
            story_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_STORY_USER_COUNT",qparams)
            print(f"Story count: {story_count}")

            generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
            user_profile = self.utils.convert_tuples_to_dicts(user_profile)
            print(f"User profile after convert_tuples_to_dicts: {user_profile}")

            user_profile[0]['profile_picture'] = user_profile[0]["profile_picture"] if not user_profile[0]["profile_picture"] else generic_code.create_media_url(SotrueAppConfig.profile_path + user_profile[0]["profile_picture"],AppConfigs.s3_profiles_folder, user_profile[0]["s3_enabled"])
            user_profile[0]['cover_image'] = user_profile[0]["cover_image"] if not user_profile[0]["cover_image"] else generic_code.create_media_url(SotrueAppConfig.profile_path + user_profile[0]["cover_image"],AppConfigs.s3_profiles_folder, user_profile[0]["s3_enabled"])
            user_profile[0]['story_count'] = story_count[0].count
            user_profile[0]['verification'] = "NOT_INITIATED" if not verification_details else verification_details[0].verified_status
            user_profile[0]['verification_comments'] = "" if not verification_details else verification_details[0].verification_comments
            print(f"User profile after media URL and verification updates: {user_profile}")

            if user_profile[0]['state'] != '-':
                qparams = [user_profile[0]['state']]
                state = self.connDB.execute_prepared_stmt("sotrueappuser","GET_CODE_DISPLAY",qparams)
                print(f"State data: {state}")
                if state:
                    user_profile[0]['state_display'] = state[0].display_value

            qparams = [self.params['req_profile_seq']]
            categories = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_CATEGORIES",qparams)
            print(f"Categories data: {categories}")
            category_list = []
            category_names = []
            for cat in categories:
                category_list.append(cat.category)
                category_names.append(cat.display_value)
            user_profile[0]['category'] = category_list
            user_profile[0]['category_display'] = category_names
            print(f"User profile after category updates: {user_profile}")

            if self.params['req_profile_seq'] != self.session.get_session_value("_profile_seq"):
                today = self.utils.get_today_date_str()
                qparams = [self.session.get_session_value("_profile_seq"),self.params['req_profile_seq'],today,today]
                subscribe_data = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_PROFILE_SUBSCRIPION",qparams)
                print(f"Subscribe data: {subscribe_data}")
                user_profile[0]['subscription_seq'] = subscribe_data[0].subscription_seq if subscribe_data else -1
                user_profile[0]['is_subscribed'] = "YES" if subscribe_data else "NO"
            else:
                user_profile[0]['subscription_seq'] = -1
                user_profile[0]['is_subscribed'] = "NO"
            print(f"User profile after subscription check: {user_profile}")

            if resctricted_data:
                user_profile[0]['restricted'] = resctricted_data[0].restriction_type
                user_profile[0]['restrict_seq'] = resctricted_data[0].restrict_seq
            else:
                user_profile[0]['restricted'] = "None"
                user_profile[0]['restrict_seq'] = -1
            print(f"User profile after restricted data check: {user_profile}")

            if resctricted_data_self:
                user_profile[0]['restricted_self'] = resctricted_data_self[0].restriction_type
                user_profile[0]['restrict_seq_self'] = resctricted_data_self[0].restrict_seq
            else:
                user_profile[0]['restricted_self'] = "None"
                user_profile[0]['restrict_seq_self'] = -1
            print(f"User profile after restricted data self check: {user_profile}")

            if user_profile[0]["profile_bio"]:
                s = [user_profile[0]["profile_bio"]]
                user_profile[0]["profile_bio_tags"] = self.validate_user_handles(s)
                user_profile[0]["profile_bio"] = s[0]
            else:
                user_profile[0]["profile_bio_tags"] = []
            print(f"User profile after bio tags: {user_profile}")
            
            qparams = [self.params["req_profile_seq"],self.utils.get_cur_timestamp(), self.utils.get_cur_timestamp()]
            user_shows = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_SHOWS_PROFILE",qparams)
            print(f"User shows: {user_shows}")
            if user_shows and user_shows[0].show_count:
                user_profile[0]["is_playlist"] = "YES"
            else:
                user_profile[0]["is_playlist"] = "NO"
            print(f"User profile after playlist check: {user_profile}")

            response = self.create_response(user_profile,"UE011")
            print(f"Final response: {response}")
            return(response)
        
        response = self.create_response(dict(),"UE012")
        print(f"Returning error response: {response}")
        return(response)
        


    def save_account_setting(self):
        if self.params["profile_type"] == "PAID":
            qparams = [self.session.get_session_value("_profile_seq")]
            free_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_FREE_MEDIA_COUNT",qparams)
            if free_count[0].count < 3:
                response = self.create_error("UE091")   
                return(response)

        qparams = [self.session.get_session_value("_profile_seq")]
        profile_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)

        qparams = [self.params["mobile"] if "mobile" in self.params else "",
                   self.session.get_session_value("_user_seq")]
        self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_USER_CONTACT",qparams)
       
        if self.params["profile_type"] == "PAID" and ("ifsc_code" not in self.params or "profile_seq" not in self.params):
            response = self.create_error("UE024")   
            return(response)

        if "country" in self.params and self.params["country"] and "state" in self.params and self.params["state"]:
            qparams = [self.params["country"],self.params["state"],self.session.get_session_value("_user_seq")]
            self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_USER_STATE_COUNTRY",qparams)

        qparams = [self.params["profile_type"],
                   "X"*(len(self.params["bank_account"])-4) + self.params["bank_account"][-4:] if "bank_account" in self.params else "",
                   self.params["ifsc_code"] if "ifsc_code" in self.params else "",
                   self.params["account_holder"] if "account_holder" in self.params else "",
                   self.params["account_type"] if "account_type" in self.params else "",
                   int(self.params["viewer_fees"])*100 if "viewer_fees" in self.params else 0,
                   self.session.get_session_value("_profile_seq")]
        self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_PROFILE_SETTING",qparams)
        self.tracker.track_update_profile()

        qparams = [self.params["gstn"] if "gstn" in self.params else "",
                   self.params["pan"] if "pan" in self.params else "",
                   self.session.get_session_value("_user_seq")]
        self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_USER_GSTIN_PAN",qparams)

        if profile_details[0].type == "PAID" and self.params["profile_type"] == "NOTPAID":
            mailer = AppEmail()
            mailer.send_email(profile_details[0].email_id,None,"SoTrue Account Switch",
            "Congratulations! \n\nYou have successfully switched your account from the monthly subscription-based model to a free model.\n\nBest,\nTeam SoTrue\n","<EMAIL>");
            qparams = ["INACTIVE",self.session.get_session_value("_profile_seq")]
            self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_SUBSCRIBED_USERS",qparams)
        elif profile_details[0].type == "NOTPAID" and self.params["profile_type"] == "PAID":
            qparams = ['FREE',0,self.session.get_session_value("_profile_seq")]
            self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_POST_TYPE",qparams)
            mailer = AppEmail()
            mailer.send_email(profile_details[0].email_id,None,"SoTrue Account Switch",
            "Congratulations! \n\nYou have successfully upgraded your account from the free model to a monthly subscription-based model.\n\nBest,\nTeam SoTrue\n","<EMAIL>");

        qparams = [self.params["user_seq"]]
        email_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_OTP",qparams)

        # Create the razorpay accounts
        contact_id = None
        account_id = None
        if  "account_holder" in self.params and self.params["account_holder"] and \
            email_data[0].email_id and email_data[0].mobile_number and \
            "ifsc_code" in self.params and self.params["ifsc_code"] and \
            "bank_account" in self.params and self.params["bank_account"]:
            razorpay = AppRazorPay()
            contact_id = razorpay.create_contact(self.params["account_holder"],email_data[0].email_id,
                                             email_data[0].mobile_number,
                                             self.session.get_session_value("_user_seq"))            
            if contact_id:
                qparams = [contact_id,self.session.get_session_value("_profile_seq")]
                self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_RAZORPAY_CONTACT",qparams)
                account_id = razorpay.create_account(contact_id,self.params["account_holder"],self.params["ifsc_code"],
                                             self.params["bank_account"])       
                if account_id:
                    qparams = [account_id,self.session.get_session_value("_profile_seq")]
                    self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_RAZORPAY_ACCOUNT",qparams)
        
        #if "bank_account" in self.params and self.params["bank_account"][0:4] != "XXXX":
        mailer = AppEmail()
        mailer.send_email(SotrueAppConfig.bank_email,None,
                          SotrueAppConfig.test_server_msg + "SoTrue User Account Details",
                "\nThe following details were submitted by an user\n\n" +
                      "Email Id: " + email_data[0].email_id + "\n" +
                      "Full Name: " + email_data[0].full_name + "\n\n" +
                      "Account Number: " + (self.params["bank_account"] if "bank_account" in self.params else "-") + "\n" +
                      "IFSC Code: " + (self.params["ifsc_code"] if "ifsc_code" in self.params else "-") + "\n" +
                      "Account Type: " + (self.params["account_type"] if "account_type" in self.params else "-") + "\n" +
                      "Benificiary Name: " + (self.params["account_holder"] if "account_holder" in self.params else "-") + "\n\n" +
                      "Razorpay Contact: " + (contact_id if contact_id else "Unable to Create") + "\n" +
                      "Razorpay Account: " + (account_id if account_id else "Unable to Create") + "\n" +
                      "\n\nThank You");

        response = self.create_response(dict(),"UE015")
        return(response)


    def get_account_setting(self):
        qparams = [self.session.get_session_value("_profile_seq")]
        user_profile = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)

        qparams = [self.params['user_seq']];
        user_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_DETAILS",qparams)

        if user_profile and user_details:
            settings = {}
            settings["account_type"] = user_profile[0].type
            settings["bank_account"] = user_profile[0].bank_account
            settings["ifsc_code"] = user_profile[0].ifsc_code
            settings["bank_account_type"] = user_profile[0].account_type
            settings["account_holder"] = user_profile[0].account_holder
            settings["email_id"] = user_details[0].email_id
            settings["mobile_number"] = user_details[0].mobile_number
            settings["country"] = user_details[0].country
            settings["state"] = user_details[0].state
            settings["gstin"] = user_details[0].gstin
            settings["pan"] = user_details[0].pan
            qparams = [user_details[0].state]
            code_display = self.connDB.execute_prepared_stmt("sotrueappuser","GET_CODE_DISPLAY",qparams)
            if code_display:
                settings["state_name"] = code_display[0].display_value
            else:
                settings["state_name"] = "-"
            settings["paid_account_fee"] = "0.00" if not user_profile[0].paid_account_fee else self.utils.format_currency(user_profile[0].paid_account_fee/100)

            response = self.create_response([settings],"UE011")
            return(response)
        
        response = self.create_response(dict(),"UE012")
        return(response)

    #
    # Returns the posts for the logged in user's home page, posts not made by the user logged in
    #
    def get_posts(self):
        qparams = [self.session.get_session_value("_profile_seq"),'ACTIVE','BLOCKED']
        restricted_by = self.connDB.execute_prepared_stmt("sotrueappuser","GET_RESTRICTED_BY",qparams)
        restricted_str = ""
        if restricted_by:
            restricted_by_list = []
            for rec in restricted_by:
                if rec.restricted_by:
                    restricted_by_list.append(rec.restricted_by)
            restricted_str = self.utils.convert_to_delim_str(restricted_by_list,",")
        
        qparams = [self.session.get_session_value("_profile_seq")]
        browsing_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_BROWSING_HISTORY",qparams)
        excluding_seq = ""
        if self.params["_start_row"] != 0 and browsing_data and browsing_data[0].content_seqs:
            excluding_seq = browsing_data[0].content_seqs
            if excluding_seq.endswith(","):
                excluding_seq = excluding_seq[0:len(excluding_seq)-1]

        if restricted_str:
            restricted_str = " AND user_posts.profile_seq NOT IN (" + restricted_str + ")"
        subs = {"<RESTRICTED_OTHERS>":restricted_str,
                "<EXCLUDING_SEQ>": excluding_seq if not excluding_seq else (" AND user_posts.post_seq NOT IN (" + excluding_seq + ")")}
        qparams = [self.session.get_session_value("_profile_seq"),self.utils.get_cur_timestamp(),self.session.get_session_value("_profile_seq"),
                   self.session.get_session_value("_profile_seq"),
                   '%' if "media_type" not in self.params or self.params["media_type"]=="ALL" else self.params["media_type"],
                   '%' if self.params["_start_row"] > 100 else "YES"]
        self.params["_rows_page"] = 50
        post_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POSTS",qparams,subs=subs,limit=self.is_paginated()) # UI always expects 10 rows
        if not post_data and qparams[len(qparams)-1] != "%":
            qparams[len(qparams)-1] = '%'
            post_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POSTS",qparams,subs=subs,limit=self.is_paginated()) # UI always expects 10 rows               

        post_data, selections = self.select_random(post_data)

        if "first_seq" in self.params:
            if self.params["first_seq"] not in selections:
                params = [self.params["first_seq"]]
                post_data_sp = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_SPECIAL",params)
                post_data.pop()
                post_data.insert(0,post_data_sp[0])
                selections.pop()
                selections.insert(0,self.params["first_seq"])
            else:
                rec_count=0
                for rec in post_data:                    
                    if rec.post_seq == self.params["first_seq"]:
                        post_data.pop(rec_count)
                        post_data.insert(0,rec)
                        break;
                    rec_count += 1

                rec_count = 0
                for seq in selections:
                   if seq == self.params["first_seq"]:
                       selections.pop(rec_count)
                       selections.insert(0,rec)
                       break;
                   rec_count += 1

        if "media_type" in self.params and self.params["media_type"] == "VIDEO":
            preferred_videos = self.get_preferred_posts()
            if preferred_videos and post_data:  # Make sure we have both data
                position = 0 if "first_seq" not in self.params else 1
                max_posts = len(post_data)  # Get current number of posts
                
                for sequence in preferred_videos:
                    # Safety check - don't exceed array bounds
                    if position >= max_posts:
                        break
                    
                    if sequence not in selections:
                        params = [sequence]
                        post_data_sp = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_SPECIAL",params)
                        
                        try:
                            if post_data_sp:
                                post_data[position] = post_data_sp[0]  # Replace instead of pop/insert
                                
                                if position < len(selections):
                                    selections[position] = sequence  # Replace in selections too
                                
                                position += 1
                                if position > 9:
                                    break
                        except Exception as e:
                            self.logger.log_error(f"Error processing preferred video {sequence}: {str(e)}")
                            continue
                    else:
                        try:
                            # Find and move existing post
                            for idx, rec in enumerate(post_data):
                                if rec.post_seq == sequence:
                                    if position < max_posts:
                                        # Swap posts instead of pop/insert
                                        post_data[position], post_data[idx] = post_data[idx], post_data[position]
                                        
                                        # Update selections accordingly
                                        if position < len(selections) and idx < len(selections):
                                            selections[position], selections[idx] = selections[idx], selections[position]
                                        
                                        position += 1
                                        if position > 9:
                                            break
                                    break
                        except Exception as e:
                            self.logger.log_error(f"Error reordering existing video {sequence}: {str(e)}")
                            continue

        if self.params["_start_row"] == 0 or not excluding_seq:
            excluding_seq = self.utils.convert_to_delim_str(selections,",")
        else:
            excluding_seq = excluding_seq + "," + self.utils.convert_to_delim_str(selections,",")
            if len(excluding_seq) > 8192:
                excess = 8192 - len(excluding_seq)
                pos = excluding_seq.index(",",excess)
                excluding = excluding[pos+1:]
        if browsing_data:
            params = [excluding_seq,self.session.get_session_value("_profile_seq")]
            self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_BROWSING_HISTORY",params)
        else:
            params = [self.session.get_session_value("_profile_seq"),excluding_seq]
            history_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_BROWSING_HISTORY",params)

        if post_data:
            post_data_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POSTS_COUNT",qparams,subs=subs) # Not used in UI
            
            post_data = self.utils.convert_tuples_to_dicts(post_data)
            post_seqs = []
            profile_seqs = []            
            for row in post_data:
                post_seqs.append(row["post_seq"])
                profile_seqs.append(row["profile_seq"])
                
            qparams = [self.session.get_session_value("_profile_seq")]
            subs = {"<POST_LIST>":self.utils.convert_to_delim_str(post_seqs,",")}                        
            post_bookmarks = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_BOOKMARKS",qparams,subs=subs)
            subscribed_posts = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_POST_SUBSCRIPION_LIST",qparams,subs=subs)
            post_comments = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_POST_COMMENTS",qparams,subs=subs)
            post_tags = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_TAG_LIST",None,subs=subs)
            post_reactions = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_REACTIONS_LIST",None,subs=subs)
            qparams.append("LIKE")
            post_likes = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_LIKES",qparams,subs=subs)
            
            post_likes_indexed = {}
            for rec in post_likes:
                post_likes_indexed[rec.post_seq] = rec.like_seq            
            post_comments_indexed = {}
            for rec in post_comments:
                post_comments_indexed[rec.content_seq] = rec.comment_seq
            post_bookmark_indexed = {}
            for rec in post_bookmarks:
                post_bookmark_indexed[rec.post_seq] = rec.bookmark_seq
            subscribed_posts_indexed = {}
            for rec in subscribed_posts:
                subscribed_posts_indexed[rec.subscribed_post_seq] = rec.subscription_seq
            post_tags_indexed = {}
            for rec in post_tags:
                post_tags_indexed[rec.post_seq] = rec.post_seq
            post_reactions_indexed = {}
            for rec in post_reactions:
                if rec.post_seq not in post_reactions_indexed:
                    post_reactions_indexed[rec.post_seq] = []
                post_reactions_indexed[rec.post_seq].append(rec.reaction)


            user_reactions_indexed = {}
            for react in self.reactions:
                query_params = [self.session.get_session_value("_profile_seq"),react]
                post_reactions = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_POST_LIKES",query_params,subs=subs)                
                for rec in post_reactions:
                    if rec.post_seq not in user_reactions_indexed:
                        user_reactions_indexed[rec.post_seq] = {}
                    if react not in user_reactions_indexed[rec.post_seq]:
                        user_reactions_indexed[rec.post_seq][react] = {"selected":"NO","count":0}        
                    user_reactions_indexed[rec.post_seq][react]["selected"] = "YES"

                query_params = [react]
                reaction_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_LIKE_COUNTS",query_params,subs=subs)
                for rec in reaction_counts:
                    if rec.post_seq not in user_reactions_indexed:
                        user_reactions_indexed[rec.post_seq] = {}
                    if react not in user_reactions_indexed[rec.post_seq]:
                        user_reactions_indexed[rec.post_seq][react] = {"selected":"NO","count":0} 
                    user_reactions_indexed[rec.post_seq][react]["count"] = rec.count

            subs = {"<PROFILE_LIST>":self.utils.convert_to_delim_str(profile_seqs,",")}
            profile_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_FOR_USERS",None,subs=subs)
            profile_data_indexed = dict()
            for row in profile_data:
                profile_data_indexed[row.profile_seq] = row
            
            today = self.utils.get_today_date_str()
            qparams = [self.session.get_session_value("_profile_seq"),'ACTIVE',today,today]
            subscriptions = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_SUBSCRIPTION_LIST",qparams,subs=subs)
            subscriptions_indexed = dict()
            for sub in subscriptions:
                subscriptions_indexed[sub.subscribed_to_seq] = sub.subscription_seq

            subs = {"<POST_LIST>":self.utils.convert_to_delim_str(post_seqs,",")}  
            qparams = ["LIKE"]
            like_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_LIKE_COUNTS",qparams,subs=subs)            
            view_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_VIEWS_COUNTS",None,subs=subs)
            share_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_SHARES_COUNTS",None,subs=subs)
            qparams = ["POST"]
            comment_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_COMMENT_COUNTS",qparams,subs=subs)            
            
            like_counts_indexed = dict()
            view_counts_indexed = dict()
            comment_counts_indexed = dict()
            share_counts_indexed = dict()
            for like in like_counts:
                like_counts_indexed[like.post_seq] = like.count
            for view in view_counts:
                view_counts_indexed[view.post_seq] = view.count
            for share in share_counts:
                share_counts_indexed[str(share.post_seq)] = share.count
            for comment in comment_counts:
                comment_counts_indexed[comment.content_seq] = comment.count

            subs = {"<PROFILE_LIST>":self.utils.convert_to_delim_str(profile_seqs,",")}
            qparams = [self.utils.get_ts_back_by(self.utils.get_cur_timestamp(),24)]
            story_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_STORY_COUNT",qparams,subs=subs)
            story_counts_indexed = dict()
            for story in story_counts:
                story_counts_indexed[story.profile_seq] = story.count

            qparams = [self.session.get_session_value("_profile_seq"),'ACTIVE','RESTRICTED']
            restricted_list = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_RESTRICTED_PROFILE_LIST",qparams,subs=subs)
            restricted_list_indexed = dict()
            for row in restricted_list:
                restricted_list_indexed[row.restricted_by] = row.restrict_seq

            gstn_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_GSTIN_LIST",None,subs=subs)
            gstn_data_indexed = {}
            for rec in gstn_data:
                if rec.gstin:
                    gstn_data_indexed[rec.profile_seq] = rec.gstin

            qparams = [self.session.get_session_value("_profile_seq")]
            subs = {"<PROFILES_LIST>":self.utils.convert_to_delim_str(profile_seqs,",")}
            following_profiles = self.connDB.execute_prepared_stmt("sotrueappuser","GET_FOLLOWED_BY_PROFILES",qparams,subs=subs)
            following_profiles_indexed = []
            for rec in following_profiles:               
                following_profiles_indexed.append(rec.following_profile_seq)            

            generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
            for row in post_data:                
                row["likes"] = 0 if row["post_seq"] not in like_counts_indexed else like_counts_indexed[row["post_seq"]]
                row["views"] = 0 if row["post_seq"] not in view_counts_indexed else view_counts_indexed[row["post_seq"]]
                row["shares"] = 0 if row["post_seq"] not in share_counts_indexed else share_counts_indexed[row["post_seq"]]
                row["comments"] = 0 if row["post_seq"] not in comment_counts_indexed else comment_counts_indexed[row["post_seq"]]                
                row["user_handle"] = profile_data_indexed[row["profile_seq"]].user_handle                
                row["profile_picture"] = profile_data_indexed[row["profile_seq"]].profile_picture if not profile_data_indexed[row["profile_seq"]].profile_picture else generic_code.create_media_url(SotrueAppConfig.profile_path + profile_data_indexed[row["profile_seq"]].profile_picture,AppConfigs.s3_profiles_folder,profile_data_indexed[row["profile_seq"]].s3_enabled)
                row["display_name"] = profile_data_indexed[row["profile_seq"]].display_name
                row["enable_comment"] = profile_data_indexed[row["profile_seq"]].enable_comment
                row["enable_watermark"] = profile_data_indexed[row["profile_seq"]].enable_watermark
                row["is_liked"] = 'YES' if row["post_seq"] in post_likes_indexed else 'NO'
                row["user_reactions"] = {} if  row["post_seq"] not in user_reactions_indexed else user_reactions_indexed[row["post_seq"]]
                row["is_bookmarked"] = 'YES' if row["post_seq"] in post_bookmark_indexed else 'NO'
                row["is_commented"] = 'YES' if row["post_seq"] in  post_comments_indexed else 'NO'
                row["posted_on"] = self.utils.get_formatted_time_past(row["posted_on"])
                row["story_count"] = 0 if row["profile_seq"] not in story_counts_indexed else story_counts_indexed[row["profile_seq"]]
                row["is_subscribed"] = 'YES' if row["post_seq"] in subscribed_posts_indexed else 'NO'
                if row["profile_seq"] not in gstn_data_indexed:                    
                    row["viewer_fee_display"] = self.utils.format_currency(int(row["viewer_fee"]/100))
                else:                    
                    row["viewer_fee_display"] = self.utils.format_currency(round((row["viewer_fee"] + (row["viewer_fee"]*0.18))/100,2))		
                row["viewer_fee"] = int(row["viewer_fee"]/100)
                row["is_restricted"] = "YES" if row["profile_seq"] in restricted_list_indexed else "NO"
                row["is_tagged"] = "YES" if row["post_seq"] in post_tags_indexed else "NO"
                row["is_following"] = "YES" if row["profile_seq"] in following_profiles_indexed else "NO"
                row["is_verified"] = profile_data_indexed[row["profile_seq"]].is_verified
                row["profile_type"] =profile_data_indexed[row["profile_seq"]].type
                if row["profile_type"] == "PAID" and row["profile_seq"] not in subscriptions_indexed:
                    row["is_profile_subscribed"] = "NO"
                else:
                    row["is_profile_subscribed"] = "YES"

                if profile_data_indexed[row["profile_seq"]].paid_account_fee:
                    if profile_data_indexed[row["profile_seq"]].gstin:
                        row["profile_fee_display"]=self.utils.format_currency(round((profile_data_indexed[row["profile_seq"]].paid_account_fee + (profile_data_indexed[row["profile_seq"]].paid_account_fee*0.18))/100,2)) 
                    else:
                        row["profile_fee_display"]=self.utils.format_currency(int(profile_data_indexed[row["profile_seq"]].paid_account_fee/100)) 
                else:
                    row["profile_fee_display"]=0

                row["profile_fee"]=int(profile_data_indexed[row["profile_seq"]].paid_account_fee)/100 if profile_data_indexed[row["profile_seq"]].paid_account_fee else 0                
                if (row["profile_type"] == "PAID" and row["is_profile_subscribed"] == "NO") or (row["post_type"] == "PAID" and row["is_subscribed"] != 'YES'):
                    row["media_file"] = ""
                    row["media_cover"]=""
                    row["is_displayed"] = "NO"
                    row["fuzzy_image"] = row["fuzzy_image"] if not row["fuzzy_image"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(row["profile_seq"]) + "/" + row["fuzzy_image"],AppConfigs.s3_posts_folder, row["s3_enabled"])
                else:
                    row["media_file"] = generic_code.create_media_url(SotrueAppConfig.media_path + str(row["profile_seq"]) + "/" + row["media_file"],AppConfigs.s3_posts_folder, row["s3_enabled"])
                    row["media_cover"] = row["media_cover"] if not row["media_cover"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(row["profile_seq"]) + "/" + row["media_cover"],AppConfigs.s3_posts_folder, row["s3_enabled"])
                    row["is_displayed"] = "YES"
                    row["fuzzy_image"] = ""
                if row["post_comments"]:
                    s = [row["post_comments"]]
                    row["post_caption_tags"] = self.validate_user_handles(s)
                    row["post_comments"] = s[0]
                else:
                    row["post_caption_tags"] = []                
                row["reactions"] = post_reactions_indexed[row["post_seq"]] if row["post_seq"] in post_reactions_indexed else []                

            qparams = [self.session.get_session_value("_profile_seq"),'ACTIVE']
            subs = {"<EXCLUDING>":""}
            show_list = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PLAYLIST",qparams,subs=subs)
            show_list = self.utils.convert_tuples_to_dicts(show_list)
            for show in show_list:            
                show["thumb_file"] = show["thumb_file"] if not show["thumb_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + show["thumb_file"],AppConfigs.s3_posts_folder,show["s3_enabled"])
                show["home_file"] = show["home_file"] if not show["home_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + show["home_file"],AppConfigs.s3_posts_folder,show["s3_enabled"])
            
            response = self.create_response(post_data,"UE011",addnl={"_total_rows":post_data_count[0].count,
                                                                     "show_list":show_list})
            return(response)
        
        response = self.create_error("UE012")   
        return(response)


    def select_random(self,post_data):
        length = len(post_data)
        selections = []
        count = 0
        if length > 10:
            while count < 10:
                num = random.randint(0, length-1)
                if num in selections:
                    continue
                selections.append(num)
                count += 1
        else:
            while count < length:
                selections.append(count)
                count += 1

        response = []
        sel_seqs = []
        for i in selections:
            response.append(post_data[i])
            sel_seqs.append(post_data[i].post_seq)
        return response, sel_seqs


    def follow_profile(self):
        qparams = [self.session.get_session_value("_profile_seq"),self.params["follow_profile_seq"],self.utils.get_cur_timestamp(),'ACTIVE']
        follower_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_FOLLOWER",qparams)
        self.tracker.track_follow(self.get_profile_user(self.params["follow_profile_seq"]))

        qparams = [self.params["follow_profile_seq"]]
        profile_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)
        qparams = [self.session.get_session_value("_profile_seq")]
        self_profile = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)

        if profile_details[0].fcm_key:
            payload = {"type":"NEW_PROFILE_FOLLOW","profile_seq":str(self.session.get_session_value("_profile_seq"))}
            fcm = AppFirebase()
            fcm.send_fcm_notification(profile_details[0].fcm_key,
                                            "New Follower Alert",
                                            "Hey! Hey! " + self_profile[0].display_name + " has followed you!",
                                            payload)

        qparams = [self.params["follow_profile_seq"]]
        profile_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)
        response = self.create_response(dict(follower_seq=follower_seq),"UE057",subs=[profile_details[0].display_name])
        return(response)
                                        

    def unfollow_profile(self):
        qparams = [self.session.get_session_value("_profile_seq"),self.params["un_profile_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappuser","DELETE_FOLLOWER",qparams)
        self.tracker.track_unfollow(self.get_profile_user(self.params["un_profile_seq"]))

        response = self.create_response(dict(),"UE058")
        return(response)


    def get_followers(self):
        qparams = [self.params["profile_seq"]]
        follower_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_FOLLOWERS",qparams,limit=self.is_paginated())

        if follower_data:
            generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger) 
            follower_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_FOLLOWER_COUNT",qparams)
            follower_data = self.utils.convert_tuples_to_dicts(follower_data)
            for follower in follower_data:
                follower["profile_picture"] = follower["profile_picture"] if not follower["profile_picture"] else generic_code.create_media_url(SotrueAppConfig.profile_path + follower["profile_picture"],AppConfigs.s3_profiles_folder, follower["s3_enabled"])
                follower["cover_image"] = follower["cover_image"] if not follower["cover_image"] else generic_code.create_media_url(SotrueAppConfig.profile_path + follower["cover_image"],AppConfigs.s3_profiles_folder, follower["s3_enabled"])

            response = self.create_response(follower_data,"UE011",addnl={"_total_rows":follower_count[0].count})
            return(response)
        
        response = self.create_error("UE012")   
        return(response)
    

    def get_follower_count(self):
        qparams = [self.params["req_profile_seq"]] # Use the profile_seq value received in the service call
        follower_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_FOLLOWER_COUNT",qparams)

        response = self.create_response({"follower_count":follower_count[0].count},"UE011")
        return(response)


    def get_subscriber_count(self):
        today = self.utils.get_today_date_str()
        qparams = [self.params["req_profile_seq"],today,today] # Use the profile_seq value received in the service call
        subscribed_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_SUBSCRIBER_COUNT",qparams)

        response = self.create_response({"subscriber_count":subscribed_count[0].count},"UE011")
        return(response)


    def get_following(self):
        qparams = [self.session.get_session_value("_profile_seq")]
        follower_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_FOLLOWING",qparams,limit=self.is_paginated())

        if follower_data:
            generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger) 
            follower_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_FOLLOWING_COUNT",qparams)
            follower_data = self.utils.convert_tuples_to_dicts(follower_data)
            for follower in follower_data:
                follower["profile_picture"] = follower["profile_picture"] if not follower["profile_picture"] else generic_code.create_media_url(SotrueAppConfig.profile_path + follower["profile_picture"],AppConfigs.s3_profiles_folder, follower["s3_enabled"])
                follower["cover_image"] = follower["cover_image"] if not follower["cover_image"] else generic_code.create_media_url(SotrueAppConfig.profile_path + follower["cover_image"],AppConfigs.s3_profiles_folder, follower["s3_enabled"])

            response = self.create_response(follower_data,"UE011",addnl={"_total_rows":follower_count[0].count})
            return(response)
        
        response = self.create_error("UE012")   
        return(response)


    def get_following_count(self):
        qparams = [self.session.get_session_value("_profile_seq")]
        follower_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_FOLLOWING_COUNT",qparams)

        response = self.create_response({"following_count":follower_count[0].count},"UE011")
        return(response)


    def add_bookmark(self):
        qparams = [self.params["post_seq"],self.session.get_session_value("_profile_seq"),self.utils.get_cur_timestamp(),'ACTIVE']
        bookmark_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_BOOKMARK",qparams)        
        self.tracker.track_add_bookmark(self.get_post_user(self.params['post_seq']))

        response = self.create_response(dict(bookmark_seq=bookmark_seq),"UE066")
        return(response)


    def remove_bookmark(self):
        qparams = [self.params["post_seq"],self.session.get_session_value("_profile_seq")]
        self.connDB.execute_prepared_stmt("sotrueappuser","DELETE_BOOKMARK",qparams)
        self.tracker.track_delete_bookmark(self.get_post_user(self.params['post_seq']))

        response = self.create_response(dict(),"UE067")
        return(response)


    def get_bookmarks(self):
        qparams = [self.session.get_session_value("_profile_seq"),'ACTIVE','BLOCKED']
        restricted_by = self.connDB.execute_prepared_stmt("sotrueappuser","GET_RESTRICTED_BY",qparams)
        restricted_str = ""
        if restricted_by:
            restricted_by_list = []
            for rec in restricted_by:
                restricted_by_list.append(rec.restricted_by)
            restricted_str = self.utils.convert_to_delim_str(restricted_by_list,",")
        
        if restricted_str:
            restricted_str = " AND user_profile.profile_seq NOT IN (" + restricted_str + ")"
        subs = {"<RESTRICTED_OTHERS>":restricted_str}
        qparams = [self.session.get_session_value("_profile_seq"),self.utils.get_cur_timestamp(),self.session.get_session_value("_profile_seq"),
                   self.session.get_session_value("_profile_seq")]
        bookmark_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_BOOKMARKS",qparams,subs=subs,limit=self.is_paginated())
        
        if bookmark_data:
            bookmark_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_BOOKMARK_COUNT",qparams,subs=subs)
            bookmark_data = self.utils.convert_tuples_to_dicts(bookmark_data)
            post_seqs = []
            profile_seqs = []
            for row in bookmark_data:
                post_seqs.append(row["post_seq"])
                profile_seqs.append(row["profile_seq"])

            subs = {"<POST_LIST>":self.utils.convert_to_delim_str(post_seqs,",")}
            qparams = [self.session.get_session_value("_profile_seq")]            
            post_views = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_VIEWS",qparams,subs=subs)
            post_comments = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_POST_COMMENTS",qparams,subs=subs)
            post_tags = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_TAG_LIST",None,subs=subs)
            post_reactions = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_REACTIONS_LIST",None,subs=subs)
            new_qparams = qparams + ["LIKE"]
            post_likes = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_LIKES",new_qparams,subs=subs)
            
            post_likes_indexed = {}
            for rec in post_likes:
                post_likes_indexed[rec.post_seq] = rec.like_seq                      
            post_views_indexed = {}
            for rec in post_views:
                post_views_indexed[rec.post_seq] = rec.view_seq          
            post_comments_indexed = {}
            for rec in post_comments:
                post_comments_indexed[rec.content_seq] = rec.comment_seq   
            post_tags_indexed = {}
            for rec in post_tags:
                post_tags_indexed[rec.post_seq] = rec.post_seq
            post_reactions_indexed = {}
            for rec in post_reactions:
                if rec.post_seq not in post_reactions_indexed:
                    post_reactions_indexed[rec.post_seq] = []
                    post_reactions_indexed[rec.post_seq].append(rec.reaction)

            user_reactions_indexed = {}
            for react in self.reactions:
                query_params = [self.session.get_session_value("_profile_seq"),react]
                post_reactions = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_POST_LIKES",query_params,subs=subs)                
                for rec in post_reactions:
                    if rec.post_seq not in user_reactions_indexed:
                        user_reactions_indexed[rec.post_seq] = {}
                    if react not in user_reactions_indexed[rec.post_seq]:
                        user_reactions_indexed[rec.post_seq][react] = {"selected":"NO","count":0}
                    user_reactions_indexed[rec.post_seq][react]["selected"] = "YES"  

                query_params = [react]
                reaction_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_LIKE_COUNTS",query_params,subs=subs)
                for rec in reaction_counts:
                    if rec.post_seq not in user_reactions_indexed:
                        user_reactions_indexed[rec.post_seq] = {}
                    if react not in user_reactions_indexed[rec.post_seq]:
                        user_reactions_indexed[rec.post_seq][react] = {"selected":"NO","count":0}
                    user_reactions_indexed[rec.post_seq][react]["count"] = rec.count

            query_params = ["LIKE"]
            like_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_LIKE_COUNTS",query_params,subs=subs)            
            view_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_VIEWS_COUNTS",None,subs=subs)
            
            subscribed_posts = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_POST_SUBSCRIPION_LIST",qparams,subs=subs)
            subscribed_posts_indexed = {}
            for rec in subscribed_posts:
                subscribed_posts_indexed[rec.subscribed_post_seq] = rec.subscription_seq

            qparams = ["POST"]
            comment_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_COMMENT_COUNTS",qparams,subs=subs) 

            like_counts_indexed = dict()            
            view_counts_indexed = dict()
            comment_counts_indexed = dict()
            for like in like_counts:
                like_counts_indexed[like.post_seq] = like.count           
            for view in view_counts:
                view_counts_indexed[view.post_seq] = view.count
            for comment in comment_counts:
                comment_counts_indexed[comment.content_seq] = comment.count

            subs = {"<PROFILE_LIST>":self.utils.convert_to_delim_str(profile_seqs,",")}
            qparams = [self.session.get_session_value("_profile_seq"),'ACTIVE','RESTRICTED']
            restricted_list = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_RESTRICTED_PROFILE_LIST",qparams,subs=subs)
            restricted_list_indexed = dict()
            for row in restricted_list:
                restricted_list_indexed[row.restricted_by] = row.restrict_seq

            today = self.utils.get_today_date_str()
            qparams = [self.session.get_session_value("_profile_seq"),'ACTIVE',today,today]
            subscriptions = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_SUBSCRIPTION_LIST",qparams,subs=subs)
            subscriptions_indexed = dict()
            for sub in subscriptions:
                subscriptions_indexed[sub.subscribed_to_seq] = sub.subscription_seq
            
            gstn_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_GSTIN_LIST",None,subs=subs)
            gstn_data_indexed = {}
            for rec in gstn_data:
                if rec.gstin:
                    gstn_data_indexed[rec.profile_seq] = rec.gstin

            qparams = [self.session.get_session_value("_profile_seq")]
            subs = {"<PROFILES_LIST>":self.utils.convert_to_delim_str(profile_seqs,",")}
            following_profiles = self.connDB.execute_prepared_stmt("sotrueappuser","GET_FOLLOWED_BY_PROFILES",qparams,subs=subs)
            following_profiles_indexed = []
            for rec in following_profiles:               
                following_profiles_indexed.append(rec.following_profile_seq)

            generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)                
            for bookmark in bookmark_data:
                bookmark["profile_picture"] = bookmark["profile_picture"] if not bookmark["profile_picture"] else generic_code.create_media_url(SotrueAppConfig.profile_path + bookmark["profile_picture"],AppConfigs.s3_profiles_folder, bookmark["s3_profile"])               
                bookmark["posted_on"] = self.utils.get_formatted_time_past(bookmark["posted_on"])
                bookmark["is_liked"] = 'YES' if bookmark["post_seq"] in post_likes_indexed else 'NO' 
                row["user_reactions"] = {} if  bookmark["post_seq"] not in user_reactions_indexed else user_reactions_indexed[bookmark["post_seq"]]
                bookmark["is_viewed"] = 'YES' if bookmark["post_seq"] in post_views_indexed else 'NO'
                bookmark["is_commented"] = 'YES' if bookmark["post_seq"] in post_comments_indexed else 'NO'
                bookmark["likes"] = 0 if bookmark["post_seq"] not in like_counts_indexed else like_counts_indexed[bookmark["post_seq"]]                
                bookmark["views"] = 0 if bookmark["post_seq"] not in view_counts_indexed else view_counts_indexed[bookmark["post_seq"]]
                bookmark["comments"] = 0 if bookmark["post_seq"] not in comment_counts_indexed else comment_counts_indexed[bookmark["post_seq"]]
                if bookmark["profile_seq"] not in gstn_data_indexed:                    
                    bookmark["viewer_fee_display"] = self.utils.format_currency(int(bookmark["viewer_fee"]/100))
                else:                    
                    bookmark["viewer_fee_display"] = self.utils.format_currency(round((bookmark["viewer_fee"] + (bookmark["viewer_fee"]*0.18))/100,2))	
                bookmark["viewer_fee"] = int(bookmark["viewer_fee"]/100)
                bookmark["is_restricted"] = "YES" if bookmark["profile_seq"] in restricted_list_indexed else "NO"
                bookmark["is_following"] = "YES" if bookmark["profile_seq"] in following_profiles_indexed else "NO"
                bookmark["media_file"] = generic_code.create_media_url(SotrueAppConfig.media_path + str(bookmark["profile_seq"]) + "/" + bookmark["media_file"],AppConfigs.s3_posts_folder, bookmark["s3_post"])
                bookmark["media_cover"] = bookmark["media_cover"] if not bookmark["media_cover"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(bookmark["profile_seq"]) + "/" + bookmark["media_cover"],AppConfigs.s3_posts_folder, bookmark["s3_post"])
                bookmark["is_displayed"] = "YES"
                bookmark["is_subscribed"] = 'YES' if bookmark["post_seq"] in subscribed_posts_indexed else 'NO'
                bookmark["is_tagged"] = "YES" if bookmark["post_seq"] in post_tags_indexed else "NO"
                if bookmark["type"] == "PAID" and bookmark["profile_seq"] not in subscriptions_indexed:
                    bookmark["is_profile_subscribed"] = "NO"
                else:
                    bookmark["is_profile_subscribed"] = "YES"
                if bookmark["post_comments"]:
                    s = [bookmark["post_comments"]]
                    bookmark["post_caption_tags"] = self.validate_user_handles(s)
                    bookmark["post_comments"] = s[0]
                else:
                    bookmark["post_caption_tags"] = []
                bookmark["reactions"] = post_reactions_indexed[bookmark["post_seq"]] if bookmark["post_seq"] in post_reactions_indexed else []

            response = self.create_response(bookmark_data,"UE011",addnl={"_total_rows":bookmark_count[0].count})
            return(response)
        
        response = self.create_error("UE037")   
        return(response)


    def get_bookmark_count(self):
        qparams = [self.session.get_session_value("_profile_seq"),'ACTIVE','BLOCKED']
        restricted_by = self.connDB.execute_prepared_stmt("sotrueappuser","GET_RESTRICTED_BY",qparams)
        restricted_str = ""
        if restricted_by:
            restricted_by_list = []
            for rec in restricted_by:
                restricted_by_list.append(rec.restricted_by)
            restricted_str = self.utils.convert_to_delim_str(restricted_by_list,",")
        
        if restricted_str:
            restricted_str = " AND user_profile.profile_seq NOT IN (" + restricted_str + ")"
        subs = {"<RESTRICTED_OTHERS>":restricted_str}
        qparams = [self.session.get_session_value("_profile_seq"),self.utils.get_cur_timestamp(),self.session.get_session_value("_profile_seq"),
                   self.session.get_session_value("_profile_seq")]
        
        bookmark_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_BOOKMARK_COUNT",qparams,subs=subs)

        response = self.create_response({"bookmark_count":bookmark_count[0].count},"UE011")
        return(response)


    def check_user_following(self):
        qparams = [self.session.get_session_value("_profile_seq"),self.params["follow_profile_seq"]]
        follow_data = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_FOLLOWING",qparams)
        if follow_data:
            response = self.create_response(dict(follower_seq=follow_data[0].follower_seq),"UE011")
            return(response)
        response = self.create_error("UE012")   
        return(response)
    

    def get_user_post(self):
        qparams = [self.params['post_seq'],self.utils.get_cur_timestamp(),self.session.get_session_value("_profile_seq")]
        post_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_SEQ_DETAILS",qparams)                
                    
        if post_data:                    
            post_data = self.utils.convert_tuples_to_dicts(post_data)            

            qparams = [self.session.get_session_value("_profile_seq"),self.params['post_seq']]
            query_params = qparams + ["LIKE"]
            post_likes = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_SEQ_LIKE",query_params)            

            comment_likes = self.connDB.execute_prepared_stmt("sotrueappuser","GET_COMMENT_SEQ_LIKE",qparams)
            post_bookmarks = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_SEQ_BOOKMARK",qparams)
            post_subscription = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_POST_SUBSCRIPION",qparams)

            qparams = [post_data[0]["profile_seq"]]
            profile_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)

            is_profile_subscribed = True
            if profile_data[0].type == "PAID" and post_data[0]["profile_seq"] != self.session.get_session_value("_profile_seq"):
                today = self.utils.get_today_date_str()
                qparams = [self.session.get_session_value("_profile_seq"),post_data[0]["profile_seq"],today,today]
                subscription = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_PROFILE_SUBSCRIPION",qparams)
                is_profile_subscribed = False if not subscription else True

            qparams = [self.params['post_seq']]
            query_params = qparams + ["LIKE"]
            like_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_SEQ_LIKE_COUNT",query_params)            
            view_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_SEQ_VIEW_COUNT",qparams)
            tag_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_TAG_COUNT",qparams)
            post_reactions = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_REACTIONS",qparams)
            reactions = []
            for reaction in post_reactions:
                reactions.append(reaction.reaction)

            user_reactions_indexed = {}
            for react in self.reactions:
                query_params = [self.params['post_seq'],react]
                post_reactions = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_SEQ_LIKE_COUNT",query_params)                
                for rec in post_reactions:
                    if self.params['post_seq'] not in user_reactions_indexed:
                        user_reactions_indexed[self.params['post_seq']] = {}
                    if react not in user_reactions_indexed[self.params['post_seq']]:
                        user_reactions_indexed[self.params['post_seq']][react] = {"selected":"NO","count":0}
                    if rec.count:
                        user_reactions_indexed[self.params['post_seq']][react]["selected"] = "YES"  
                    user_reactions_indexed[self.params['post_seq']][react]["count"] = rec.count                    


            qparams = [self.params['post_seq'],"POST"]
            comment_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_SEQ_COMMENT_COUNT",qparams)                            

            qparams = [self.utils.get_ts_back_by(self.utils.get_cur_timestamp(),24),post_data[0]["profile_seq"],
                       self.session.get_session_value("_profile_seq")]
            story_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_STORY_USER_COUNT",qparams)

            qparams = [post_data[0]["profile_seq"],self.session.get_session_value("_profile_seq"),'RESTRICTED','ACTIVE']
            restricted_data = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_RESTRICTED_PROFILE",qparams)
            
            generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
            for row in post_data:                
                row["likes"] = like_counts[0].count                
                row["views"] = view_counts[0].count
                row["comments"] = comment_counts[0].count   
                row["is_tagged"] = "YES" if tag_counts.count else "NO"
                row["user_handle"] = profile_data[0].user_handle                
                row["profile_picture"] = profile_data[0].profile_picture if not profile_data[0].profile_picture else generic_code.create_media_url(SotrueAppConfig.profile_path + profile_data[0].profile_picture,AppConfigs.s3_profiles_folder, profile_data[0].s3_enabled)
                row["display_name"] = profile_data[0].display_name
                row["enable_comment"] = profile_data[0].enable_comment
                row["enable_watermark"] = profile_data[0].enable_watermark
                row["is_liked"] = 'YES' if post_likes else 'NO'    
                row["user_reactions"] = user_reactions_indexed[self.params['post_seq']]
                row["is_commented"] = 'YES' if comment_likes else 'NO'
                row["is_bookmarked"] = 'YES' if post_bookmarks else 'NO'
                row["posted_on"] = self.utils.get_formatted_time_past(row["posted_on"])
                row["story_count"] = story_count[0].count
                row["is_subscribed"] = 'YES' if post_subscription else 'NO'
                if profile_data[0].gstin:
                    row["viewer_fee_display"] = self.utils.format_currency(round((row["viewer_fee"] + (row["viewer_fee"]*0.18))/100,2))                    
                else:                    
                    row["viewer_fee_display"] = self.utils.format_currency(int(row["viewer_fee"]/100))
                row["viewer_fee"] = int(row["viewer_fee"]/100)
                row["is_restricted"] = "NO" if not restricted_data else "YES"
                row["is_verified"] = profile_data[0].is_verified 
                row["profile_type"] = profile_data[0].type

                if profile_data[0].paid_account_fee:
                    if profile_data[0].gstin:
                        row["profile_fee_display"]=self.utils.format_currency(round((profile_data[0].paid_account_fee + (profile_data[0].paid_account_fee*0.18))/100,2)) 
                    else:
                        row["profile_fee_display"]=self.utils.format_currency(int(profile_data[0].paid_account_fee/100)) 
                else:
                   row["profile_fee_display"]=0

                row["profile_fee"] = int(profile_data[0].paid_account_fee)/100 if profile_data[0].paid_account_fee else 0                
                row["is_profile_subscribed"] = "YES" if is_profile_subscribed else "NO"
                if not is_profile_subscribed or (row["post_type"] == "PAID" and row["is_subscribed"] != 'YES'):
                    row["media_file"] = ""
                    row["media_cover"]=""
                    row["is_displayed"] = "NO"
                    row["fuzzy_image"] = row["fuzzy_image"] if not row["fuzzy_image"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(row["profile_seq"]) + "/" + row["fuzzy_image"],AppConfigs.s3_posts_folder, row["s3_enabled"])
                else:
                    row["media_file"] = generic_code.create_media_url(SotrueAppConfig.media_path + str(row["profile_seq"]) + "/" + row["media_file"],AppConfigs.s3_posts_folder, row["s3_enabled"])
                    row["media_cover"] = row["media_cover"] if not row["media_cover"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(row["profile_seq"]) + "/" + row["media_cover"],AppConfigs.s3_posts_folder, row["s3_enabled"])
                    row["is_displayed"] = "YES"
                    row["fuzzy_image"] = ""
                if row["post_comments"]:
                    s = [row["post_comments"]]
                    row["post_caption_tags"] = self.validate_user_handles(s)
                    row["post_comments"] = s[0]
                else:
                    row["post_caption_tags"] = []
                row["reactions"] = reactions

            response = self.create_response(post_data,"UE011")
            return(response)
        
        response = self.create_error("UE012")   
        return(response)


    def get_profile_suggestions(self):
        qparams = [self.session.get_session_value("_profile_seq"),'ACTIVE','BLOCKED']
        restricted_by = self.connDB.execute_prepared_stmt("sotrueappuser","GET_RESTRICTED_BY",qparams)
        restricted_str = ""
        if restricted_by:
            restricted_by_list = []
            for rec in restricted_by:
                restricted_by_list.append(rec.restricted_by)
            restricted_str = self.utils.convert_to_delim_str(restricted_by_list,",")
        
        if restricted_str:
            restricted_str = " AND profile_follower.following_profile_seq NOT IN (" + restricted_str + ")"
        subs = {"<RESTRICTED_OTHERS>":restricted_str}
        qparams = [self.session.get_session_value("_profile_seq"),self.session.get_session_value("_profile_seq"),
                   self.session.get_session_value("_profile_seq")]
        max_follower_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_SORTED_ON_FOLLOWERS",qparams,subs=subs,limit=self.is_paginated())

        if max_follower_data:
            qparams = [self.session.get_session_value("_profile_seq"),self.session.get_session_value("_profile_seq"),
                       self.session.get_session_value("_profile_seq"),30]
            max_follower_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_SORTED_ON_FOLLOWERS_COUNT",qparams,subs=subs)
            
            follower_list = []
            for row in max_follower_data:
                follower_list.append(row.following_profile_seq)

            subs = {"<PROFILE_LIST>":self.utils.convert_to_delim_str(follower_list,",")}
            profile_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_FOR_USERS",None,subs=subs)

            profile_data = self.utils.convert_tuples_to_dicts(profile_data)
            generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger) 
            for row in profile_data:                        
                row["profile_picture"] = row["profile_picture"] if not row["profile_picture"] else generic_code.create_media_url(SotrueAppConfig.profile_path + row["profile_picture"],AppConfigs.s3_profiles_folder, row["s3_enabled"])
                row["cover_image"] = row["cover_image"] if not row["cover_image"] else generic_code.create_media_url(SotrueAppConfig.profile_path + row["cover_image"],AppConfigs.s3_profiles_folder, row["s3_enabled"])
            response = self.create_response(profile_data,"UE011",addnl={"_total_rows":len(max_follower_count) if len(max_follower_count)<30 else 30})
            return(response)
            
        response = self.create_error("UE060")
        return(response)


    def submit_story(self):
        if self.params["uploads"] is not None:
            qparams = [self.session.get_session_value("_profile_seq")]
            profile_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PRIVACY",qparams)
            file_path = self.configs.temp_folder_path + self.params["uploads"][0]["upload_name"]
            target_path = SotrueAppConfig.media_path + str(self.session.get_session_value("_profile_seq"))
            file_name = self.params["uploads"][0]["upload_name"]
            if not self.utils.folder_exists(target_path):
                self.utils.create_folder(target_path)
            target_path += "/" + self.params["uploads"][0]["upload_name"]              
            if profile_data[0].enable_watermark=="YES" and self.params["media_type"] == "IMAGE":
                self.utils.watermark_image(file_path,SotrueAppConfig.watermark_file,target_path)
                resize_extn = self.utils.resize_image(target_path)
                target_path = target_path[0:target_path.rfind(".")+1] + resize_extn
                if AppConfigs.s3_enabled:
                    file_name = self.utils.get_uuid_str() + self.params["uploads"][0]["upload_name"][self.params["uploads"][0]["upload_name"].rfind("."):]
                    awsS3 = AppAwsS3()
                    awsS3.send_media_file(target_path, file_name, AppConfigs.s3_stories_folder)                      
            elif self.params["media_type"] == "VIDEO":
                self.utils.validate_video_file(file_path)
                self.utils.copy_files(file_path,target_path)
                if AppConfigs.s3_enabled:
                    file_name = self.utils.get_uuid_str() + self.params["uploads"][0]["upload_name"][self.params["uploads"][0]["upload_name"].rfind("."):]
                    awsS3 = AppAwsS3()
                    awsS3.send_media_file(file_path, file_name, AppConfigs.s3_stories_folder)                  
            elif self.params["media_type"] == "IMAGE":
                resize_extn = self.utils.resize_image(file_path)
                file_path = file_path[0:file_path.rfind(".")+1] + resize_extn
                target_path = target_path[0:target_path.rfind(".")+1] + resize_extn
                self.utils.copy_files(file_path,target_path)
                if AppConfigs.s3_enabled:
                    file_name = self.utils.get_uuid_str() + self.params["uploads"][0]["upload_name"][self.params["uploads"][0]["upload_name"].rfind("."):]
                    awsS3 = AppAwsS3()
                    awsS3.send_media_file(file_path, file_name, AppConfigs.s3_stories_folder)                  
                                    
            if not AppConfigs.s3_enabled:
                self.cache_uploaded_file(self.params["uploads"][0]["key"],target_path)
            self.utils.delete_file(file_path)

            frame_path = None
            duration = 0
            if self.params["media_type"] == "VIDEO":                
                frame_path = target_path[0:target_path.rfind(".")] + "_frame" + ".jpg"
                if not self.utils.get_first_video_frame(target_path,frame_path):
                    if AppConfigs.s3_enabled:
                        frame_file_name = self.utils.get_uuid_str() + ".jpg"
                        awsS3 = AppAwsS3()
                        awsS3.send_media_file(SotrueAppConfig.default_video_image, frame_file_name, AppConfigs.s3_stories_folder)
                        frame_path = "/" + frame_file_name
                    else:
                        self.utils.copy_files(SotrueAppConfig.default_video_image,frame_path)
                elif AppConfigs.s3_enabled:
                    frame_file_name = self.utils.get_uuid_str() + ".jpg"
                    awsS3 = AppAwsS3()
                    awsS3.send_media_file(frame_path, frame_file_name, AppConfigs.s3_stories_folder)
                    frame_path = "/" + frame_file_name

                if not AppConfigs.s3_enabled:
                    self.cache_uploaded_file(self.params["uploads"][0]["key"] + "_frame",frame_path)
                duration = self.utils.get_video_duration(target_path)

            qparams = [self.session.get_session_value("_profile_seq"),
                       self.params["caption"].replace("\n","<br>") if "caption" in self.params else "",
                       self.utils.get_cur_timestamp(),
                       self.utils.get_ts_ahead_by(self.utils.get_cur_timestamp(),24),
                       self.params["media_type"],
                       self.utils.get_file_size(target_path),
                       self.params["file_format"],
                       file_name,
                       "ACTIVE",frame_path if not frame_path else frame_path[frame_path.rfind("/")+1:],duration,
                       "YES" if AppConfigs.s3_enabled else "NO"]
            story_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_STORY",qparams)   
            self.tracker.track_story()
            
            self.insert_tags(self.params["caption"] if "caption" in self.params else "","STORY_CAPTION",
                            story_seq)

            if AppConfigs.s3_enabled:
                self.utils.delete_file(target_path)
            response = self.create_response(dict(story_seq=story_seq),"UE025")
            return(response)
            
        else:
            response = self.create_error("UE009")   
            return(response)


    def delete_story(self):
        qparams = ['INACTIVE',self.params["story_seq"],self.session.get_session_value("_profile_seq")]
        self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_STORY_STATUS",qparams)
        self.tracker.track_delete_story(self.get_story_user(self.params['story_seq']))

        response = self.create_response(dict(),"UE014")
        return(response)
        

    def get_stories(self):
        qparams = [self.utils.get_ts_back_by(self.utils.get_cur_timestamp(),24),
                    self.session.get_session_value("_profile_seq"),self.session.get_session_value("_profile_seq"),50]
        story_list = self.connDB.execute_prepared_stmt("sotrueappuser","GET_STORY_LIST",qparams)

        selected_stories = []
        if story_list:
            story_list = self.utils.convert_tuples_to_dicts(story_list)
            generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)  
            final_list = []
            for row in story_list:
                if row["profile_seq"] not in selected_stories:
                    selected_stories.append(row["profile_seq"])
                    row["profile_picture"] = row["profile_picture"] if not row["profile_picture"] else generic_code.create_media_url(SotrueAppConfig.profile_path + row["profile_picture"],AppConfigs.s3_profiles_folder, row["s3_enabled"])
                    final_list.append(row)                     

            response = self.create_response(final_list,"UE011")
            return(response)
        
        response = self.create_error("UE012")
        return(response)


    def get_profile_stories(self):
        subs = {}
        qparams = [self.utils.get_ts_back_by(self.utils.get_cur_timestamp(),24),self.params['profile_seq'],
                   self.session.get_session_value("_profile_seq")] # Use the profile_seq value received in the service call
        subs["<STATUS_QUERY>"] = "='ACTIVE'" if self.params['profile_seq'] != self.session.get_session_value("_profile_seq") else "!='INACTIVE'"
        story_list = self.connDB.execute_prepared_stmt("sotrueappuser","GET_STORY_USER",qparams,subs=subs)

        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
        if story_list:
            story_list = self.utils.convert_tuples_to_dicts(story_list)
            for row in story_list:                        
                row["profile_picture"] = row["profile_picture"] if not row["profile_picture"] else generic_code.create_media_url(SotrueAppConfig.profile_path + row["profile_picture"],AppConfigs.s3_profiles_folder, row["s3_profile"])
                row["media_file"] = generic_code.create_media_url(SotrueAppConfig.media_path + str(self.params["profile_seq"]) + "/" + row["media_file"],AppConfigs.s3_stories_folder, row["s3_story"])
                row["media_cover"] = row["media_cover"] if not row["media_cover"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(self.params["profile_seq"]) + "/" + row["media_cover"],AppConfigs.s3_stories_folder, row["s3_story"])
                if row["media_type"] == "VIDEO" and row["video_duration"] == 0:
                    duration = self.utils.get_video_duration(SotrueAppConfig.media_path + str(self.params["profile_seq"]) + "/" + row["media_file"])
                    row["video_duration"] = duration
                    qparams [duration, row["story_seq"]]
                    self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_VIDEO_DURATION_STORY",qparams)
                if row["story_comments"]:
                    s = [row["story_comments"]]
                    row["story_comment_tags"] = self.validate_user_handles(s)
                    row["story_comments"] = s[0]
                else:
                    row["story_comment_tags"] = []

            response = self.create_response(story_list,"UE011")
            return(response)
        
        response = self.create_error("UE012")
        return(response)


    def get_media_data(self):
        target_path = SotrueAppConfig.media_path + str(self.session.get_session_value("_profile_seq"))
        self.ensure_directory_exists(target_path)
        ts = self.utils.get_cur_timestamp();
        ts = self.utils.get_ts_back_by_mins(ts,SotrueAppConfig.media_expiry_mins)       
        qparams = [ts]
        self.connDB.execute_prepared_stmt("sotruegeneric","DELETE_MEDIA_FILE",qparams,replication=False)

        qparams = [self.params["media_key"],self.session.get_session_value("_ip_address")]
        media_file = self.connDB.execute_prepared_stmt("sotruegeneric","GET_MEDIA_FILE",qparams)
        file_path = SotrueAppConfig.missing_media_file        
        if media_file:
            file_path = media_file[0].file_path                 

        self.session.set_session_value("_RAW_RESPONSE",True)
        return(file_path)


    def get_media_url_post(self):
        qparams = [self.params["post_seq"]] # Use the profile_seq value received in the service call
        post_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_SEQ",qparams)

        if post_data:
            generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
            # Use the profile_seq value received in the service call
            media_url = generic_code.create_media_url(SotrueAppConfig.media_path + str(self.params["profile_seq"]) + "/" + post_data[0].media_file, AppConfigs.s3_posts_folder, post_data[0].s3_enabled)
            response = self.create_response(dict(media_url=media_url),"UE011")
            return(response)
        
        response = self.create_error("UE012")
        return(response)


    def get_media_url_profile(self):
        qparams = [self.session.get_session_value("_profile_seq")]
        profile_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)

        if profile_data:
            generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
            profile_picture = profile_data[0].profile_picture if not profile_data[0].profile_picture else generic_code.create_media_url(SotrueAppConfig.profile_path + profile_data[0].profile_picture,AppConfigs.s3_profiles_folder, profile_data[0].s3_enabled)
            cover_image = profile_data[0].cover_image if not profile_data[0].cover_image else generic_code.create_media_url(SotrueAppConfig.profile_path + profile_data[0].cover_image,AppConfigs.s3_profiles_folder, profile_data[0].s3_enabled)
            response = self.create_response(dict(profile_url=profile_picture,cover_url=cover_image),"UE011")
            return(response)
        
        response = self.create_error("UE012")
        return(response)


    def get_privacy_settings(self):
        qparams = [self.session.get_session_value("_profile_seq")]
        privacy_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PRIVACY",qparams)        
        if privacy_data:
            privacy_data = self.utils.convert_tuples_to_dicts(privacy_data)
            response = self.create_response(privacy_data,"UE011")
            return(response)
        
        response = self.create_error("UE012")
        return(response)


    def update_privacy_settings(self):
        qparams = [self.params["show_fan_count"],self.params["show_media_count"],self.params["enable_comment"],
                   self.params["enable_watermark"],self.session.get_session_value("_profile_seq")]
        self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_PRIVACY",qparams)
        response = self.create_response(dict(),"UE015")
        return(response)


    def get_media_count(self):
        qparams = [self.params["profile_seq"],  # Use the profile_seq value received in the service call
                   self.utils.get_today_date_str()]
        count_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_MEDIA_COUNT",qparams)

        response = self.create_response(dict(media_count=count_data[0].count),"UE011")
        return(response)


    def do_search(self):
        if "search_str" in self.params:
            search_str = "'%" + self.params["search_str"] + "%'"
            subs = {"<USER_HANDLE>": search_str,"<DISP_NAME>": search_str,"<CATEGORY_LIST>":search_str,"<SKIP_PROFILE>":str(self.session.get_session_value("_profile_seq"))}        
            profiles = self.connDB.execute_prepared_stmt("sotrueappuser","SEARCH_PROFILES",None,subs=subs,limit=self.is_paginated())
            profile_count = self.connDB.execute_prepared_stmt("sotrueappuser","COUNT_SEARCH_PROFILES",None,subs=subs)
            if not profiles:
                profiles = self.connDB.execute_prepared_stmt("sotrueappuser","SEARCH_PROFILES_INTERESTS",None,subs=subs,limit=self.is_paginated())
                profile_count = self.connDB.execute_prepared_stmt("sotrueappuser","COUNT_SEARCH_PROFILES_INTERESTS",None,subs=subs)

            if profiles:                
                profile_seqs = []
                for rec in profiles:
                    profile_seqs.append(rec.profile_seq)

                subs = {"<PROFILE_LIST>":self.utils.convert_to_delim_str(profile_seqs,",")}
                search_results = self.connDB.execute_prepared_stmt("sotrueappuser","SEARCH_PROFILES_DATA",None,subs=subs)

                generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
                search_results = self.utils.convert_tuples_to_dicts(search_results)
                for row in search_results:
                    row["profile_picture"] = row["profile_picture"] if not row["profile_picture"] else generic_code.create_media_url(SotrueAppConfig.profile_path + row["profile_picture"],AppConfigs.s3_profiles_folder, row["s3_enabled"])
                
                response = self.create_response(search_results,"UE011",addnl={"_total_rows":profile_count[0].count})
                return(response)
        else:
            subs = {"<SKIP_PROFILE>":str(self.session.get_session_value("_profile_seq"))}
            profiles = self.connDB.execute_prepared_stmt("sotrueappuser","SEARCH_PROFILES_DEFAULT",None,subs=subs,limit=self.is_paginated())
        
            if profiles:
                profile_count = self.connDB.execute_prepared_stmt("sotrueappuser","COUNT_SEARCH_PROFILES_DEFAULT",None,subs=subs)

                profile_seqs = []
                for rec in profiles:
                    profile_seqs.append(rec.profile_seq)

                subs = {"<PROFILE_LIST>":self.utils.convert_to_delim_str(profile_seqs,",")}
                search_results = self.connDB.execute_prepared_stmt("sotrueappuser","SEARCH_PROFILES_DATA",None,subs=subs)

                generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
                search_results = self.utils.convert_tuples_to_dicts(search_results)
                for row in search_results:
                    row["profile_picture"] = row["profile_picture"] if not row["profile_picture"] else generic_code.create_media_url(SotrueAppConfig.profile_path + row["profile_picture"],AppConfigs.s3_profiles_folder, row["s3_enabled"])
                
                response = self.create_response(search_results,"UE011",addnl={"_total_rows":profile_count[0].count})
                return(response)

        response = self.create_error("UE063")
        return(response)
        

    def do_playlist_search(self):
        if "search_str" in self.params:
            search_str = "'%" + str(self.params["search_str"]) + "%'"
            search_date = "'" + self.utils.get_cur_timestamp() + "'"
            subs = {"<TITLE>": search_str,"<DESCRIPTION>": search_str, "<TOPIC>": search_str,
                    "<EXP_DATE>":search_date,"<SCHED_DATE>":search_date,
                    "<USER_SEQ>":self.session.get_session_value("_user_seq")}
            self.logger.log_message(subs)
            playlists = self.connDB.execute_prepared_stmt("sotrueappuser","SEARCH_PLAYLIST",None,subs=subs,limit=self.is_paginated())

            if playlists:
                playlist_count = self.connDB.execute_prepared_stmt("sotrueappuser","COUNT_SEARCH_PLAYLIST",None,subs=subs)
                show_seqs = []
                for rec in playlists:
                    show_seqs.append(rec.show_seq)
                subs = {"<SHOW_SEQ_LIST>":self.utils.convert_to_delim_str(show_seqs,",")}
                playlists = self.connDB.execute_prepared_stmt("sotrueappuser","SEARCH_PLAYLIST_DETAILS",None,subs=subs)

                generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
                search_results = self.utils.convert_tuples_to_dicts(playlists)
                for show in search_results:
                    show["logo_file"] = show["logo_file"] if not show["logo_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + show["logo_file"],AppConfigs.s3_posts_folder,show["s3_enabled"])
                    show["thumb_file"] = show["thumb_file"] if not show["thumb_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + show["thumb_file"],AppConfigs.s3_posts_folder,show["s3_enabled"])
                    
                response = self.create_response(search_results,"UE011",addnl={"_total_rows":playlist_count[0].count})
                return(response)
        else:            
            qparams = [self.utils.get_cur_timestamp(),self.utils.get_cur_timestamp()]
            playlists = self.connDB.execute_prepared_stmt("sotrueappuser","SEARCH_PLAYLIST_DEFAULT",qparams,limit=self.is_paginated())

            if playlists:
                playlist_count = self.connDB.execute_prepared_stmt("sotrueappuser","COUNT_SEARCH_PLAYLIST_DEFAULT",qparams)

                generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
                search_results = self.utils.convert_tuples_to_dicts(playlists)
                for show in search_results:
                    show["logo_file"] = show["logo_file"] if not show["logo_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + show["logo_file"],AppConfigs.s3_posts_folder,show["s3_enabled"])
                    show["thumb_file"] = show["thumb_file"] if not show["thumb_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + show["thumb_file"],AppConfigs.s3_posts_folder,show["s3_enabled"])

                response = self.create_response(search_results,"UE011",addnl={"_total_rows":playlist_count[0].count})
                return(response)

        response = self.create_error("UE063")
        return(response)


    def do_topic_search(self):
        if "search_str" in self.params:
            search_str = "'%" + self.params["search_str"] + "%'"
            subs = {"<TOPIC>": search_str, "<EXPIRE_DATE>":"'" + self.utils.get_cur_timestamp() + "'",
                    "<PROFILE_SEQ>":self.session.get_session_value("_profile_seq")}                              
            topics = self.connDB.execute_prepared_stmt("sotrueappuser","SEARCH_TOPIC",None,subs=subs,limit=self.is_paginated())

            if topics:
                topic_count = self.connDB.execute_prepared_stmt("sotrueappuser","COUNT_SEARCH_TOPIC",None,subs=subs)

                post_list = []
                for rec in topics:
                    post_list.append(rec.post_seq)
                subs = {"<POST_LIST>":self.utils.convert_to_delim_str(post_list,",")}
                topics = self.connDB.execute_prepared_stmt("sotrueappuser","GET_SEARCH_TOPIC_DETAILS",None,subs=subs)
                self.logger.log_message(subs)

                profile_seqs = []        
                for row in topics:                    
                    profile_seqs.append(row.profile_seq)                
                subs = {"<PROFILE_LIST>":self.utils.convert_to_delim_str(profile_seqs,",")}
                today = self.utils.get_today_date_str()
                qparams = [self.session.get_session_value("_profile_seq"),'ACTIVE',today,today]
                subscriptions = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_SUBSCRIPTION_LIST",qparams,subs=subs)
                subscriptions_indexed = dict()
                for sub in subscriptions:
                    subscriptions_indexed[sub.subscribed_to_seq] = sub.subscription_seq

                post_seqs = []
                for rec in topics:
                    if rec.post_seq not in post_seqs:
                        post_seqs.append(rec.post_seq)        
                subs = {"<POST_LIST>":self.utils.convert_to_delim_str(post_seqs,",")}
                qparams = [self.session.get_session_value("_profile_seq")]   
                self.logger.log_message(subs)
                self.logger.log_message(qparams)
                subscribed_posts = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_POST_SUBSCRIPION_LIST",qparams,subs=subs)
                self.logger.log_message(subscribed_posts)
                subscribed_posts_indexed = {}
                for rec in subscribed_posts:
                    subscribed_posts_indexed[rec.subscribed_post_seq] = rec.subscription_seq
                self.logger.log_message(subscribed_posts_indexed)

                generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
                search_results = self.utils.convert_tuples_to_dicts(topics)
                for row in search_results:
                    row["fuzzy_image"] = row["fuzzy_image"] if not row["fuzzy_image"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(row["profile_seq"]) + "/" + row["fuzzy_image"],AppConfigs.s3_posts_folder, row["s3_enabled"])            
                    row["media_file"] = generic_code.create_media_url(SotrueAppConfig.media_path + str(row["profile_seq"]) + "/" + row["media_file"],AppConfigs.s3_posts_folder, row["s3_enabled"])
                    row["media_cover"] = row["media_cover"] if not row["media_cover"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(row["profile_seq"]) + "/" + row["media_cover"],AppConfigs.s3_posts_folder, row["s3_enabled"])
                    row["is_subscribed"] = 'YES' if str(row["post_seq"]) in subscribed_posts_indexed else 'NO'
                    if row["profile_type"] == "PAID" and row["profile_seq"] in subscriptions_indexed:
                        row["is_profile_subscribed"] = "YES"
                    else:
                        row["is_profile_subscribed"] = "NO"

                response = self.create_response(search_results,"UE011",addnl={"_total_rows":topic_count[0].count})
                return(response)
        else:            
            qparams = [self.utils.get_cur_timestamp()]
            topics = self.connDB.execute_prepared_stmt("sotrueappuser","SEARCH_TOPIC_DEFAULT",qparams,limit=self.is_paginated())

            if topics:
                topic_count = self.connDB.execute_prepared_stmt("sotrueappuser","COUNT_SEARCH_TOPIC_DEFAULT",qparams)

                profile_seqs = []        
                for row in topics:                    
                    profile_seqs.append(row.profile_seq)                
                subs = {"<PROFILE_LIST>":self.utils.convert_to_delim_str(profile_seqs,",")}
                today = self.utils.get_today_date_str()
                qparams = [self.session.get_session_value("_profile_seq"),'ACTIVE',today,today]
                subscriptions = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_SUBSCRIPTION_LIST",qparams,subs=subs)
                subscriptions_indexed = dict()
                for sub in subscriptions:
                    subscriptions_indexed[sub.subscribed_to_seq] = sub.subscription_seq

                post_seqs = []
                for rec in topics:
                    if rec.post_seq not in post_seqs:
                        post_seqs.append(rec.post_seq)        
                subs = {"<POST_LIST>":self.utils.convert_to_delim_str(post_seqs,",")}
                qparams = [self.session.get_session_value("_profile_seq")]                        
                subscribed_posts = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_POST_SUBSCRIPION_LIST",qparams,subs=subs)
                subscribed_posts_indexed = {}
                for rec in subscribed_posts:
                    subscribed_posts_indexed[rec.subscribed_post_seq] = rec.subscription_seq

                generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
                search_results = self.utils.convert_tuples_to_dicts(topics)
                for row in search_results:
                    row["fuzzy_image"] = row["fuzzy_image"] if not row["fuzzy_image"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(row["profile_seq"]) + "/" + row["fuzzy_image"],AppConfigs.s3_posts_folder, row["s3_enabled"])            
                    row["media_file"] = generic_code.create_media_url(SotrueAppConfig.media_path + str(row["profile_seq"]) + "/" + row["media_file"],AppConfigs.s3_posts_folder, row["s3_enabled"])
                    row["media_cover"] = row["media_cover"] if not row["media_cover"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(row["profile_seq"]) + "/" + row["media_cover"],AppConfigs.s3_posts_folder, row["s3_enabled"])
                    row["is_subscribed"] = 'YES' if str(row["post_seq"]) in subscribed_posts_indexed else 'NO'
                    if row["profile_type"] == "PAID" and row["profile_seq"] not in subscriptions_indexed:
                        row["is_profile_subscribed"] = "NO"
                    else:
                        row["is_profile_subscribed"] = "YES"

                response = self.create_response(search_results,"UE011",addnl={"_total_rows":topic_count[0].count})
                return(response)

        response = self.create_error("UE063")
        return(response)


    def type_ahead_search(self):
        search_str = "'%" + self.params["search_str"] + "%'"
        subs = {"<USER_HANDLE>": search_str,"<DISP_NAME>": search_str}
        search_results = self.connDB.execute_prepared_stmt("sotrueappuser","SEARCH_PROFILES_TYPEAHEAD",None,subs=subs)

        if search_results:            
            search_results = self.utils.convert_tuples_to_dicts(search_results)            
            response = self.create_response(search_results,"UE011")
            return(response)
        
        response = self.create_error("UE012")
        return(response)


    def get_payment_order(self):

        qparams = [self.params["req_profile_seq"]]
        gstin_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_GSTIN_PROFILE",qparams)
        tax = 0
        if gstin_data and gstin_data[0].gstin:
            tax = round(int(self.params["amount"]) * 0.18,2)

        razorpay = AppRazorPay()
        transaction = str(self.session.get_session_value("_profile_seq")) + "_" + str(AppSecurity.get_random_otp(12))
        order_id = razorpay.get_order_id((int(self.params["amount"])+tax), transaction)

        qparams = [self.utils.get_cur_timestamp(), self.session.get_session_value("_profile_seq"), self.params["user_seq"],
                   (int(self.params["amount"])+tax)*100, "INITIATED", order_id, transaction]
        init_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_PAYMENT_INIT",qparams)

        qparams = [self.params["user_seq"]]
        user_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_DETAILS",qparams)        

        response = self.create_response(dict(order_id=order_id,init_seq=init_seq,
                                             key=AppConfigs.razorpay_key_id,
                                             amount=(int(self.params["amount"])+tax)*100,
                                             currency="INR", app_name="Sotrue App",
                                             description="Profile Payment", name=user_data[0].full_name,
                                             email=user_data[0].email_id,
                                             contact="9999999999" if not user_data[0].mobile_number else user_data[0].mobile_number),"UE011")
        return(response)


    def get_payment_hash(self):   
        qparams = [self.params["user_seq"]]
        user_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_DETAILS",qparams)

        transaction = str(self.session.get_session_value("_profile_seq")) + "_" + str(AppSecurity.get_random_otp(12))
        hash_str = AppConfigs.payu_merchant_key + "|" + transaction + "|" + str(int(self.params["amount"])) + "|SotrueApp|" + user_data[0].full_name + "|" + user_data[0].email_id + "|||||||||||" + AppConfigs.payu_merchant_salt
        hash = AppSecurity.hash512(hash_str)

        qparams = [self.utils.get_cur_timestamp(), self.session.get_session_value("_profile_seq"), self.params["user_seq"],
                   int(self.params["amount"])*100, "INITIATED", hash, transaction,self.params["redirect"],self.params["subscribe_type"],
                   self.params["subscribe_seq"]]
        init_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_PAYMENT_INIT_HASH",qparams)
        
        response = self.create_response(dict(init_seq=init_seq,hash=hash,
                                             key=AppConfigs.payu_merchant_key,
                                             txnid=transaction,
                                             amount=int(self.params["amount"]),
                                             productinfo="SotrueApp",
                                             firstname=user_data[0].full_name,
                                             email=user_data[0].email_id,
                                             surl=AppConfigs.payu_redirect,
                                             furl=AppConfigs.payu_redirect,
                                             payu_url=AppConfigs.payu_url,
                                             phone="9999999999" if not user_data[0].mobile_number else user_data[0].mobile_number),"UE011")
        return(response)


    def update_payment_order(self):
        if "raw_resp" not in self.params:
            razorpay = AppRazorPay()
            if not razorpay.check_payment_success(self.params["order_id"]):
                response = self.create_response(dict(),"UE035")
                return(response)

        qparams = [self.params["init_seq"],self.params["status"],self.params["amount"],
                   self.params["raw_resp"] if "raw_resp" in self.params else "{}",
                   self.utils.get_cur_timestamp()]
        status_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_PAYMENT_STATUS",qparams)        

        if self.params["status"] == "SUCCESS":            

            qparams = [self.params["init_seq"],self.session.get_session_value("_profile_seq"),self.params["user_seq"],self.params["order_id"]]
            payment_init_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PAYMENT_INIT",qparams)

            if not payment_init_data or payment_init_data[0].status == "SUCCESS" or payment_init_data[0].gateway_id != self.params["order_id"] or self.params['amount'] != payment_init_data[0].amount:
                response = self.create_error("UE034")
                return(response)

            if "raw_resp" in self.params:
                response_json = self.utils.parse_json(self.params["raw_resp"])
                pay_id = response_json["razorpay_payment_id"]
                order_id = response_json["razorpay_order_id"]
                signature=response_json["razorpay_signature"]
                try:
                    razorpay = AppRazorPay()
                    razorpay.verify_signature(self.params["order_id"],pay_id,signature)
                except:
                    response = self.create_error("UE034")
                    return(response)
            
            qparams = [self.params["status"], self.params["init_seq"],self.params["order_id"]]
            self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_INIT_STATUS",qparams)
        
            qparams = [self.session.get_session_value("_profile_seq")]
            profile_balance = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_BALANCE",qparams)

            new_balance = profile_balance[0].profile_balance + int(payment_init_data[0].amount)
            qparams = [new_balance,self.session.get_session_value("_profile_seq")]
            self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_PROFILE_BALANCE",qparams)

            qparams = [self.session.get_session_value("_profile_seq"),self.params["user_seq"],payment_init_data[0].amount,
                               self.utils.get_cur_timestamp(),status_seq]
            payment_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_PAYMENT_RECORD",qparams)

            response = self.create_response(dict(profile_balance=int(new_balance/100),
                                                 display_balance=self.utils.format_currency(int(new_balance/100))),"UE015")
            self.tracker.track_payment_success()
            return(response)
        else:
            qparams = [self.params["status"], self.params["init_seq"],self.params["order_id"]]
            self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_INIT_STATUS",qparams)
        
        response = self.create_response(dict(),"UE035")
        return(response)


    def get_pay_order_status(self):
        razorpay = AppRazorPay()
        if razorpay.check_payment_success(self.params["order_id"]):
            response = self.create_response(dict(),"UE035")
            return(response)
        else:
            response = self.create_error("UE034")
            return(response)


    def get_payu_response(self):    
        
        qparams = [self.params["txnid"]]
        payment_init_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PAYMENT_INIT_HASH",qparams)
       
        raw_resp = self.utils.to_json(self.params)
        if not payment_init_data:
            qparams = [0,"missing",int(float(self.params["amount"]))*100,raw_resp,self.utils.get_cur_timestamp()]
        elif payment_init_data[0].status == "SUCCESS":
            qparams = [payment_init_data[0].init_seq,"duplicate",int(float(self.params["amount"]))*100,raw_resp,self.utils.get_cur_timestamp()]
        else:
            qparams = [payment_init_data[0].init_seq,self.params["status"],int(float(self.params["amount"]))*100,raw_resp,self.utils.get_cur_timestamp()]

        status_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_PAYMENT_STATUS",qparams)

        if self.params["status"] == "success":            
            if not payment_init_data or payment_init_data[0].status == "SUCCESS" or int(float(self.params['amount']))*100 != payment_init_data[0].amount:
                qparams = ["MISMATCH", self.params["mihpayid"], payment_init_data[0].init_seq]
                self.session.set_session_value("_REDIRECT_RESPONSE",True) #UE0034
                return(payment_init_data[0].redirect_url)

            if "additionalCharges" in self.params and self.params["additionalCharges"]:
                hash_str = str(self.params["additionalCharges"]) + "|" + AppConfigs.payu_merchant_salt + "|" + self.params["status"] + "|||||||||||" + self.params["email"] + "|" + self.params["firstname"] + "|" + self.params["productinfo"] + "|" + str(self.params["amount"]) + "|" + str(self.params["txnid"]) + "|" + AppConfigs.AppConfigs.payu_merchant_key
            else:
                hash_str = AppConfigs.payu_merchant_salt + "|" + self.params["status"] + "|||||||||||" + self.params["email"] + "|" + self.params["firstname"] + "|" + self.params["productinfo"] + "|" + str(self.params["amount"]) + "|" + str(self.params["txnid"]) + "|" + AppConfigs.payu_merchant_key

            hash = AppSecurity.hash512(hash_str)
            if self.params["hash"] != hash:
                qparams = ["HASHERR", self.params["mihpayid"], payment_init_data[0].init_seq]
                self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_INIT_STATUS_HASH",qparams)
                self.session.set_session_value("_REDIRECT_RESPONSE",True)
                return(payment_init_data[0].redirect_url)
            else:

                qparams = ["SUCCESS", self.params["mihpayid"], payment_init_data[0].init_seq]
                self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_INIT_STATUS_HASH",qparams)

                qparams = [payment_init_data[0].profile_seq]
                profile_balance = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_BALANCE",qparams)

                new_balance = profile_balance[0].profile_balance + int(payment_init_data[0].amount)
                qparams = [new_balance,payment_init_data[0].profile_seq]
                self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_PROFILE_BALANCE",qparams)

                qparams = [payment_init_data[0].profile_seq,payment_init_data[0].user_seq,payment_init_data[0].amount,
                                   self.utils.get_cur_timestamp(),status_seq]
                payment_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_PAYMENT_RECORD",qparams)

                if payment_init_data[0].subscribe_to == "POST":
                    resp = self.subscribe_post(payment_init_data[0].subscribe_seq,payment_init_data[0].profile_seq,payment_init_data[0].user_seq)                   
                else:                    
                    resp = self.subscribe_profile(payment_init_data[0].subscribe_seq,payment_init_data[0].profile_seq,payment_init_data[0].user_seq)
                #resp = self.utils.parse_json(resp)
                self.session.set_session_value("_REDIRECT_RESPONSE",True)
                return(payment_init_data[0].redirect_url)
        else:
            qparams = [self.params["status"], self.params["mihpayid"], payment_init_data[0].init_seq]
            self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_INIT_STATUS_HASH",qparams)
            self.session.set_session_value("_REDIRECT_RESPONSE",True)
            return(payment_init_data[0].redirect_url)


    def get_payment_status(self):
        qparams = [self.params["init_seq"]]
        payment_init_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PAYMENT_INIT_DATA",qparams)         
        if payment_init_data[0].subscribe_to == "POST":
            qparams = [payment_init_data[0].profile_seq,payment_init_data[0].subscribe_seq]
            sub_data = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_POST_SUBSCRIPION",qparams)
        else:
            today = self.utils.get_today_date_str()
            qparams = [payment_init_data[0].profile_seq,payment_init_data[0].subscribe_seq,today,today]
            sub_data = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_PROFILE_SUBSCRIPION",qparams)
        if payment_init_data[0].status == "SUCCESS" and sub_data:
            response = self.create_response(dict(),"UE026")
        else:
            response = self.create_error("UE027")
        return(response)


    def get_profile_balance(self):
        qparams = [self.session.get_session_value("_profile_seq")]
        profile_balance = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_BALANCE",qparams)

        response = self.create_response(dict(profile_balance=int(profile_balance[0].profile_balance/100),
                                             display_balance=self.utils.format_currency(int(profile_balance[0].profile_balance/100))),"UE011")
        return(response)


    def get_subscribe_details(self):
        qparams = [self.params["subscribe_profile_seq"]]
        profile_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)
        qparams = [self.session.get_session_value("_profile_seq")]
        balance_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_BALANCE",qparams)
        if profile_details and profile_details[0].type == "PAID":
            monthly_charge_disp= '0.00'
            if profile_details[0].gstin:
               monthly_charge_disp=self.utils.format_currency(round((profile_details[0].paid_account_fee + (profile_details[0].paid_account_fee*0.18))/100,2)) 
            else:
                monthly_charge_disp=self.utils.format_currency(int(profile_details[0].paid_account_fee/100)) 
            response = self.create_response(dict(monthly_charge= 0 if not profile_details[0].paid_account_fee else int(profile_details[0].paid_account_fee/100),
                                                 monthly_charge_disp=monthly_charge_disp,
                                                 balance=self.utils.format_currency(int(balance_details[0].profile_balance/100)),
                                                 display_balance=self.utils.format_currency(int(balance_details[0].profile_balance/100))),"UE011")
            return(response)        
        response = self.create_error("UE012")
        return(response)
    

    def subscribe_profile(self,subscribe_profile=None,profile_seq=None,user_seq=None):
        if not subscribe_profile:
            subscribe_profile = self.params["subscribe_profile_seq"]
        if not profile_seq:
            profile_seq = self.session.get_session_value("_profile_seq")
        if not user_seq:
            user_seq = self.params["user_seq"]

        if subscribe_profile == profile_seq:
            response = self.create_error("UE029")
            return(response)

        today = self.utils.get_today_date_str()
        qparams = [profile_seq,subscribe_profile,today,today]
        subscribe_data = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_PROFILE_SUBSCRIPION",qparams)
        if subscribe_data:
            response = self.create_error("UE031")
            return(response)
        
        qparams = [subscribe_profile]
        profile_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)
        qparams = [profile_seq]
        balance_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_BALANCE",qparams)                    
            
        if profile_details and profile_details[0].type == "PAID":
            if int(balance_details[0].profile_balance) < profile_details[0].paid_account_fee:
                response = self.create_error("UE028")
                return(response)
            else:
                charges = self.compute_subscription_charges(profile_details[0].user_seq, profile_details[0].paid_account_fee)
                qparams = [profile_seq,user_seq,profile_details[0].paid_account_fee,self.utils.get_cur_timestamp(),"PROFILE",subscribe_profile,
                           charges["gst"],charges["inward_charges"], charges["outward_charges"],charges["comission"],
                           charges["gst_comission"], charges["tcs"], charges["tds"], charges["payable"]]
                charge_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_CHARGE_RECORD",qparams)
                
                qparams = [profile_seq,subscribe_profile,charge_seq,today,self.utils.advance_date_by(today,30),'ACTIVE']
                subscription_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_PROFILE_SUBSCRIPTION",qparams)
                self.tracker.track_subscribe_profile(self.get_profile_user(subscribe_profile))

                new_balance = int(balance_details[0].profile_balance) - profile_details[0].paid_account_fee
                qparams = [new_balance,profile_seq]
                self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_PROFILE_BALANCE",qparams)

                # Add auto following, if not already following
                qparams = [profile_seq,subscribe_profile]
                follower_data = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_FOLLOWING",qparams)
                if not follower_data:
                    qparams = [profile_seq,subscribe_profile,self.utils.get_cur_timestamp(),'ACTIVE']
                    follower_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_FOLLOWER",qparams)

                qparams = [profile_seq]
                self_profile = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)

                if profile_details[0].enable_email_alerts == "YES":
                    mailer = AppEmail()                     
                    mailer.send_email(profile_details[0].email_id,None,"New Subscriber Alert",
                        "\nCongratulations! " + self_profile[0].display_name + " has subscribed to your profile. Looking forward to all your new and exclusive content.\n\nYour Biggest Fans,\nTeam SoTrue");                   

                if profile_details[0].fcm_key:
                    payload = {"type":"NEW_PROFILE_SUB","profile_seq":str(profile_seq)}
                    fcm = AppFirebase()
                    fcm.send_fcm_notification(profile_details[0].fcm_key,
                                                    "New Subscriber Alert",
                                                    "Congratulations!\n\n" + self_profile[0].display_name + " has just subscribed to your profile. We're excited to see all your new and exclusive content.\n\nBest,\nTeam SoTrue\n",                                                      
                                                  payload)

                self.update_earnings(profile_details[0].user_seq,profile_details[0].paid_account_fee)

                response = self.create_response(dict(subscription_seq=subscription_seq,
                                                     balance=int(new_balance/100),
                                                     display_balance=self.utils.format_currency(int(new_balance/100))),"UE026")
                return(response)                
                
        response = self.create_error("UE027")
        return(response)


    def subscribe_post(self,post_seq=None,profile_seq=None,user_seq=None):                
        if not post_seq:
            post_seq = self.params["post_seq"]
        if not profile_seq:
            profile_seq = self.session.get_session_value("_profile_seq")
        if not user_seq:
            user_seq = self.params["user_seq"]
        qparams = [post_seq]
        content_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_SEQ",qparams)
        
        if profile_seq == content_details[0].profile_seq:
            response = self.create_error("UE030")
            return(response)

        qparams = [profile_seq,post_seq]
        subscription_data = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_POST_SUBSCRIPION",qparams)
        if subscription_data:
            response = self.create_error("UE031")
            return(response)

        qparams = [profile_seq]
        balance_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_BALANCE",qparams)
            
        if content_details[0].post_type == "PAID":
            if int(balance_details[0].profile_balance) < content_details[0].viewer_fee:
                response = self.create_error("UE028")
                return(response)
            else:
                qparams = [content_details[0].profile_seq]
                user_seq_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_USER_SEQ",qparams)
                charges = self.compute_subscription_charges(user_seq_data[0].user_seq, content_details[0].viewer_fee)
                qparams = [profile_seq,user_seq,content_details[0].viewer_fee,self.utils.get_cur_timestamp(),"POST",post_seq,
                           charges["gst"],charges["inward_charges"], charges["outward_charges"],charges["comission"],
                           charges["gst_comission"], charges["tcs"], charges["tds"], charges["payable"]]
                charge_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_CHARGE_RECORD",qparams)

                today = self.utils.get_today_date_str()
                qparams = [profile_seq,post_seq,charge_seq,'ACTIVE',
                               self.utils.get_cur_timestamp(), content_details[0].profile_seq]
                subscription_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_POST_SUBSCRIPTION",qparams)
                self.tracker.track_subscribe_post(self.get_post_user(post_seq))

                new_balance = int(balance_details[0].profile_balance) - content_details[0].viewer_fee
                qparams = [new_balance,profile_seq]
                self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_PROFILE_BALANCE",qparams)

                qparams = [content_details[0].profile_seq]
                profile_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)
                qparams = [profile_seq]
                self_profile = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)                    

                if profile_details[0].enable_email_alerts == "YES":
                    mailer = AppEmail()                    
                    mailer.send_email(profile_details[0].email_id,None,"New Subscriber Alert",
                        "\nCongratulations! " + self_profile[0].display_name + " has subscribed to your post. Looking forward to all your new and exclusive content.\n\nYour Biggest Fans,\nTeam SoTrue");

                if profile_details[0].fcm_key:
                    payload = {"type":"NEW_POST_SUB","post_seq":str(post_seq)}
                    fcm = AppFirebase()
                    fcm.send_fcm_notification(profile_details[0].fcm_key,
                                                      "New Subscriber Alert",
                                                      "Wohoo! " + self_profile[0].display_name + " has subscribed to your post!",
                                                      payload)

                self.update_earnings(profile_details[0].user_seq,content_details[0].viewer_fee)                

                response = self.create_response(dict(subscription_seq=subscription_seq,balance=int(new_balance/100),
                                                     display_balance=self.utils.format_currency(int(new_balance/100))),"UE026")
                return(response)                
                
        response = self.create_error("UE027")
        return(response)


    def update_earnings(self,user_seq,fees):
        qparams = [user_seq]
        referral = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_OTP",qparams)        
        if referral[0].used_referral_seq:
            qparams = [referral[0].used_referral_seq]
            referral_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_REFERRAL_DETAILS",qparams)            
            if referral_details:
                days_diff = self.utils.get_date_diff_days_str(referral_details[0].used_on,self.utils.get_cur_timestamp())                
                if days_diff <= referral_details[0].referral_duration:
                    total_earnings = self.get_total_earnings(referral_details[0].user_seq)
                    qparams = [referral_details[0].user_seq]
                    user_details  = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_DETAILS",qparams)
                    print("Fees " + str(fees))
                    print("referral_pc " + str(referral_details[0].referral_pc))
                    earned = int(fees*(referral_details[0].referral_pc/10000))
                    gst = 0
                    if user_details[0].gstin:
                         gst = int(earned * 0.18)
                    tax = 0
                    if total_earnings >= 15000*100:
                        if user_details[0].pan:
                            tax = int(earned*0.1)
                        else:
                            tax = int(earned*0.2)

                    charges = int(round((earned+gst-tax)*0.00118))
                    
                    qparams = [referral[0].used_referral_seq, self.utils.get_cur_timestamp(),
                            earned,
                            referral_details[0].user_seq,gst,tax,charges, earned + gst - tax - charges]
                    earning_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_REFERRAL_EARNED",qparams)


    def get_total_earnings(self,user_seq):
        fiscal = self.utils.get_fiscal()
        qparams = [user_seq, fiscal["start"] + " 00:00:00", fiscal["end"] + " 23:59:59"]
        referal_earnings = self.connDB.execute_prepared_stmt("sotrueappuser","GET_REFERRAL_EARNINGS",qparams)
        qparams = [user_seq]
        user_profile = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_PROFILE_SEQ",qparams)
        qparams = [user_profile[0].profile_seq,"2021-01-01 00:00:00",self.utils.get_cur_timestamp()]
        payments_received_profile = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PAYMENTS_RECEIVED_FOR_PROFILE",qparams)   
        payments_received_posts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PAYMENTS_RECEIVED_FOR_POSTS",qparams)

        total_earnings = (referal_earnings[0].amount_earned if referal_earnings[0].amount_earned else 0) + \
                        (payments_received_profile[0].sum if payments_received_profile[0].sum else 0) + \
                        (payments_received_posts[0].sum if payments_received_posts[0].sum else 0)
        return total_earnings


    def compute_subscription_charges(self,user_seq,fees):
        total_earnings = self.get_total_earnings(user_seq)
        qparams = [user_seq]
        user_details  = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_DETAILS",qparams)
        user_comission = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_COMISSION",qparams)
        pct_comission = 2000
        if user_comission:
            pct_comission = user_comission[0].percentage
        
        gst = 0
        tcs = 0
        if user_details[0].gstin:
            gst = int(fees * 0.18)
            tcs = fees * 0.01
        inward = (fees + gst) * (0.019 * 1.18)
        comission = fees * (pct_comission/10000)
        gst_comission =  comission * (0.18)
        
        tds = fees * 0.05
        if user_details[0].pan:
            if user_details[0].pan[3] == 'P' or user_details[0].pan[3] == 'H':
                if total_earnings <= 500000*100:
                    tds = 0
        outward = (fees + gst - inward - comission - gst_comission - tcs - tds) * 0.00118
        payable = (fees + gst - inward - comission - gst_comission - tcs - tds - outward)

        payouts = {"gst":gst,"inward_charges":inward,"outward_charges":outward,"comission":comission,
                   "gst_comission":gst_comission, "tcs":tcs,"tds":tds,"payable":payable,"paid_amount":fees}
        return(payouts);

        
    def get_subscribed_profiles(self):
        subscribed_profiles = None
        today = self.utils.get_today_date_str()
        if self.params["type"] == "ACTIVE":            
            qparams = [self.session.get_session_value("_profile_seq"),today,today]            
            subscribed_profiles = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_SUBSCRIPTIONS",qparams,limit=self.is_paginated())
        else:
            qparams = [self.session.get_session_value("_profile_seq"),today]
            subscribed_profiles = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_SUBSCRIPTIONS_PAST",qparams,limit=self.is_paginated())
            
        if subscribed_profiles:
            subscribed_count = None
            if self.params["type"] == "ACTIVE":
                qparams = [self.session.get_session_value("_profile_seq"),today,today]
                subscribed_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_SUBSCRIPTIONS_COUNT",qparams)
            else:
                qparams = [self.session.get_session_value("_profile_seq"),today]
                subscribed_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_SUBSCRIPTIONS_PAST_COUNT",qparams)
                
            subscribed_profiles = self.utils.convert_tuples_to_dicts(subscribed_profiles)
            generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
            for profile in subscribed_profiles:
                profile['profile_picture'] = profile["profile_picture"] if not profile["profile_picture"] else generic_code.create_media_url(SotrueAppConfig.profile_path + profile["profile_picture"],AppConfigs.s3_profiles_folder, profile["s3_enabled"])
                profile['cover_image'] = profile['cover_image'] if not profile['cover_image'] else generic_code.create_media_url(SotrueAppConfig.profile_path + profile['cover_image'],AppConfigs.s3_profiles_folder, profile["s3_enabled"])
                profile['from_date'] = self.utils.format_date_display(profile['from_date'])
                profile['to_date'] = self.utils.format_date_display(profile['to_date'])
            response = self.create_response(subscribed_profiles,"UE011",addnl={"_total_rows":subscribed_count[0].count})
            return(response)
        
        response = self.create_error("UE012")
        return(response)


    def get_subscribed_posts(self):
        qparams = [self.session.get_session_value("_profile_seq"),'ACTIVE','BLOCKED']
        restricted_by = self.connDB.execute_prepared_stmt("sotrueappuser","GET_RESTRICTED_BY",qparams)
        restricted_str = ""
        if restricted_by:
            restricted_by_list = []
            for rec in restricted_by:
                restricted_by_list.append(rec.restricted_by)
            restricted_str = self.utils.convert_to_delim_str(restricted_by_list,",")
        
        if restricted_str:
            restricted_str = " AND post_subscription.subscribed_to_seq NOT IN (" + restricted_str + ")"
        subs = {"<RESTRICTED_OTHERS>":restricted_str}
        qparams = [self.session.get_session_value("_profile_seq"),self.utils.get_cur_timestamp(),self.session.get_session_value("_profile_seq"),
                   self.session.get_session_value("_profile_seq")]        
        subscribed_posts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_SUBSCRIPTIONS",qparams,subs=subs,limit=self.is_paginated())

        if subscribed_posts:
            subscribed_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_SUBSCRIPTIONS_COUNT",qparams,subs=subs)
            subscribed_posts = self.utils.convert_tuples_to_dicts(subscribed_posts)
            post_seqs = []
            profile_seqs = []
            for row in subscribed_posts:
                post_seqs.append(row["post_seq"])
                profile_seqs.append(row["profile_seq"])

            subs = {"<POST_LIST>":self.utils.convert_to_delim_str(post_seqs,",")} 
            qparams = [self.session.get_session_value("_profile_seq")] 
            query_params = qparams + ["LIKE"]
            post_likes = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_POST_LIKES",query_params,subs=subs)
            post_views = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_POST_VIEWS",qparams,subs=subs)
            post_comments = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_POST_COMMENTS",qparams,subs=subs)
            post_bookmarks = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_BOOKMARKS",qparams,subs=subs)
            post_tags = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_TAG_LIST",None,subs=subs)
            post_reactions = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_REACTIONS_LIST",None,subs=subs)

            post_likes_indexed = {}
            for rec in post_likes:
                post_likes_indexed[rec.post_seq] = rec.like_seq              
            post_views_indexed = {}
            for rec in post_views:
                post_views_indexed[rec.post_seq] = rec.view_seq  
            post_comments_indexed = {}
            for rec in post_comments:
                post_comments_indexed[rec.content_seq] = rec.comment_seq
            post_bookmark_indexed = {}
            for rec in post_bookmarks:
                post_bookmark_indexed[rec.post_seq] = rec.bookmark_seq
            post_tags_indexed = {}
            for rec in post_tags:
                post_tags_indexed[rec.post_seq] = rec.post_seq
            post_reactions_indexed = {}
            for rec in post_reactions:
                if rec.post_seq not in post_reactions_indexed:
                    post_reactions_indexed[rec.post_seq] = []
                post_reactions_indexed[rec.post_seq].append(rec.reaction)


            user_reactions_indexed = {}
            for react in self.reactions:
                query_params = [self.session.get_session_value("_profile_seq"),react]
                post_reactions = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_POST_LIKES",query_params,subs=subs)                
                for rec in post_reactions:
                    if rec.post_seq not in user_reactions_indexed:
                        user_reactions_indexed[rec.post_seq] = {}
                    if react not in user_reactions_indexed[rec.post_seq]:
                        user_reactions_indexed[rec.post_seq][react] = {"selected":"NO","count":0}
                    user_reactions_indexed[rec.post_seq][react]["selected"] = "YES"  

                query_params = [react]
                reaction_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_LIKE_COUNTS",query_params,subs=subs)
                for rec in reaction_counts:
                    if rec.post_seq not in user_reactions_indexed:
                        user_reactions_indexed[rec.post_seq] = {}
                    if react not in user_reactions_indexed[rec.post_seq]:
                        user_reactions_indexed[rec.post_seq][react] = {"selected":"NO","count":0}
                    user_reactions_indexed[rec.post_seq][react]["count"] = rec.count


            query_params = ["LIKE"]
            like_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_LIKE_COUNTS",query_params,subs=subs)            
            view_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_VIEWS_COUNTS",None,subs=subs)
            qparams = ["POST"]
            comment_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_COMMENT_COUNTS",qparams,subs=subs)

            like_counts_indexed = dict()
            view_counts_indexed = dict()
            comment_counts_indexed = dict()
            for like in like_counts:
                like_counts_indexed[like.post_seq] = like.count            
            for view in view_counts:
                view_counts_indexed[view.post_seq] = view.count
            for comment in comment_counts:
                comment_counts_indexed[comment.content_seq] = comment.count

            subs = {"<PROFILE_LIST>":self.utils.convert_to_delim_str(profile_seqs,",")}
            profile_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_FOR_USERS",None,subs=subs)
            profile_data_indexed = dict()
            for row in profile_data:
                profile_data_indexed[row.profile_seq] = row

            qparams = [self.session.get_session_value("_profile_seq"),'ACTIVE','RESTRICTED']
            restricted_list = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_RESTRICTED_PROFILE_LIST",qparams,subs=subs)
            restricted_list_indexed = dict()
            for row in restricted_list:
                restricted_list_indexed[row.restricted_by] = row.restrict_seq
            
            qparams = [self.utils.get_ts_back_by(self.utils.get_cur_timestamp(),24)]
            story_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_STORY_COUNT",qparams,subs=subs)
            story_counts_indexed = dict()
            for story in story_counts:
                story_counts_indexed[story.profile_seq] = story.count
                
            qparams = [self.session.get_session_value("_profile_seq")]
            subs = {"<PROFILES_LIST>":self.utils.convert_to_delim_str(profile_seqs,",")}
            following_profiles = self.connDB.execute_prepared_stmt("sotrueappuser","GET_FOLLOWED_BY_PROFILES",qparams,subs=subs)
            following_profiles_indexed = []
            for rec in following_profiles:               
                following_profiles_indexed.append(rec.following_profile_seq)

            generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
            for row in subscribed_posts:
                row["likes"] = 0 if row["post_seq"] not in like_counts_indexed else like_counts_indexed[row["post_seq"]]                
                row["views"] = 0 if row["post_seq"] not in view_counts_indexed else view_counts_indexed[row["post_seq"]]
                row["comments"] = 0 if row["post_seq"] not in comment_counts_indexed else comment_counts_indexed[row["post_seq"]]
                row["is_liked"] = 'YES' if row["post_seq"] in post_likes_indexed else 'NO'
                row["user_reactions"] = {} if  row["post_seq"] not in user_reactions_indexed else user_reactions_indexed[row["post_seq"]]
                row["is_viewed"] = 'YES' if row["post_seq"] in post_views_indexed else 'NO'
                row["is_commented"] = 'YES' if row["post_seq"] in post_comments_indexed else 'NO'
                row["is_bookmarked"] = 'YES' if row["post_seq"] in post_bookmark_indexed else 'NO'
                row['profile_picture'] = row["profile_picture"] if not row["profile_picture"] else generic_code.create_media_url(SotrueAppConfig.profile_path + row["profile_picture"],AppConfigs.s3_profiles_folder, row["s3_profile"])
                row['subscribed_on'] = self.utils.format_date_display(row['subscribed_on'])               
                row["posted_on"] = self.utils.get_formatted_time_past(row["posted_on"])
                row["story_count"] = 0 if row["profile_seq"] not in story_counts_indexed else story_counts_indexed[row["profile_seq"]]
                row["enable_comment"] = profile_data_indexed[row["profile_seq"]].enable_comment
                row["enable_watermark"] = profile_data_indexed[row["profile_seq"]].enable_watermark
                row["is_restricted"] = "YES" if row["profile_seq"] in restricted_list_indexed else "NO"
                row["is_following"] = "YES" if row["profile_seq"] in following_profiles_indexed else "NO"
                row["media_file"] = generic_code.create_media_url(SotrueAppConfig.media_path + str(row["profile_seq"]) + "/" + row["media_file"],AppConfigs.s3_posts_folder, row["s3_post"])
                row["media_cover"] = row["media_cover"] if not row["media_cover"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(str(row["profile_seq"])) + "/" + row["media_cover"],AppConfigs.s3_posts_folder, row["s3_post"])
                row["is_displayed"] = "YES"
                row["is_tagged"] = "YES" if row["post_seq"] in post_tags_indexed else "NO"
                if row["post_comments"]:
                    s=[row["post_comments"]]
                    row["post_caption_tags"] = self.validate_user_handles(s)
                    row["post_comments"] = s[0]
                else:
                    row["post_caption_tags"] = []
                row["reactions"] = post_reactions_indexed[row["post_seq"]] if row["post_seq"] in post_reactions_indexed else []
                
            response = self.create_response(subscribed_posts,"UE011",addnl={"_total_rows":subscribed_count[0].count})
            return(response)
        
        response = self.create_error("UE012")
        return(response)


    def unsubscribe_profile(self):
        today = self.utils.get_today_date_str()
        qparams = [self.session.get_session_value("_profile_seq"),self.params["subscribe_profile_seq"],today,today]
        subscribe_data = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_PROFILE_SUBSCRIPION",qparams)
        if not subscribe_data:
            response = self.create_error("UE032")
            return(response)
        qparams = ["UNSUBS",self.utils.get_cur_timestamp(),self.params["subscription_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_PROFILE_SUBSCRIPTION",qparams)
        self.tracker.track_unsubscribe_profile(self.get_profile_user(self.params["subscribe_profile_seq"]))

        qparams = [self.params["subscribe_profile_seq"]]
        profile_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)

        response = self.create_response(dict(),"UE033",subs=[profile_details[0].display_name])
        return(response)


    def get_payments_credit(self):
        qparams = [self.params["user_seq"],self.session.get_session_value("_profile_seq")]
        payments_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PAYMENTS_CREDIT",qparams,limit=self.is_paginated())
        if payments_data:
            payments_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PAYMENTS_CREDIT_COUNT",qparams)            
            payments_data = self.utils.convert_tuples_to_dicts(payments_data)
            for payment in payments_data:
                payment["paid_on"] = self.utils.format_ts_display(payment["paid_on"])
                payment["paid_amount"] = self.utils.format_currency(int(payment["paid_amount"]/100))
            response = self.create_response(payments_data,"UE011",addnl={"_total_rows":payments_count[0].count})
            return(response)
        
        response = self.create_error("UE012")
        return(response)

    
    def get_payments_debit(self):
        qparams = [self.params["user_seq"],self.session.get_session_value("_profile_seq")]
        payments_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PAYMENTS_DEBIT",qparams,limit=self.is_paginated())
        if payments_data:
            payments_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PAYMENTS_DEBIT_COUNT",qparams)

            profiles = []
            posts = []
            for payment in payments_data:
                if payment.paid_for == "PROFILE":
                    profiles.append(payment.paid_key)
                if payment.paid_for == "POST":
                    posts.append(payment.paid_key)

            profiles_indexed = {}            
            if profiles:
                subs = {"<PROFILE_LIST>":self.utils.convert_to_delim_str(profiles,",")}
                profile_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_FOR_USERS",None,subs=subs)
                for profile in profile_data:
                    profiles_indexed[profile.profile_seq] = profile
                    
            posts_indexed = {}
            if posts:
                subs = {"<POST_LIST>":self.utils.convert_to_delim_str(posts,",")}
                post_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_USER_DETAILS",None,subs=subs)
                for post in post_data:
                    posts_indexed[post.post_seq] = post
                
            payments_data = self.utils.convert_tuples_to_dicts(payments_data)
            for payment in payments_data:
                payment["paid_on"] = self.utils.format_ts_display(payment["paid_on"])
                payment["paid_amount"] = self.utils.format_currency(int(payment["paid_amount"]/100))
                payment["paid_for"] = "Profile Subscription" if payment["paid_for"]=="PROFILE" else "Post Subscription"
                if payment["paid_for"] == "PROFILE":
                    payment["user"] =  profiles_indexed[profile["paid_key"]] if profile["paid_key"] in profiles_indexed else "-"
                elif payment["paid_for"] == "POST":
                    payment["user"] = posts_indexed[profile["paid_key"]] if profile["paid_key"] in posts_indexed else "-"
                    
            response = self.create_response(payments_data,"UE011",addnl={"_total_rows":payments_count[0].count})
            return(response)
        
        response = self.create_error("UE012")
        return(response)


    def get_user_profile_subscribers(self):
        today = self.utils.get_today_date_str()
        qparams = [self.session.get_session_value("_profile_seq"),today,today]        
        profile_subscribers = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_SUBSCRIBERS",qparams,limit=self.is_paginated())        
        if profile_subscribers:
            profile_subscribers_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_SUBSCRIBERS_COUNT",qparams)
            profile_subscribers = self.utils.convert_tuples_to_dicts(profile_subscribers)
            generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)   
            for subscriber in profile_subscribers:  
                subscriber['from_date'] = self.utils.format_date_display(subscriber['from_date'])
                subscriber['to_date'] = self.utils.format_date_display(subscriber['to_date'])
                subscriber['paid_amount'] = self.utils.format_currency(subscriber['paid_amount']/100)
                subscriber['profile_picture'] = subscriber['profile_picture'] if not subscriber['profile_picture'] else generic_code.create_media_url(SotrueAppConfig.profile_path + subscriber['profile_picture'],AppConfigs.s3_profiles_folder)
                subscriber['cover_image'] = subscriber['cover_image'] if not subscriber['cover_image'] else generic_code.create_media_url(SotrueAppConfig.profile_path + subscriber['cover_image'],AppConfigs.s3_profiles_folder)
            response = self.create_response(profile_subscribers,"UE011",addnl={"_total_rows":profile_subscribers_count[0].count})
            return(response)
            
        response = self.create_error("UE012")
        return(response)


    def get_user_post_subscribers(self):
        qparams = [self.session.get_session_value("_profile_seq")]        
        post_subscribers = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_SUBSCRIBERS",qparams,limit=self.is_paginated())        
        if post_subscribers:
            post_subscribers_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_SUBSCRIBERS_COUNT",qparams)
            post_subscribers = self.utils.convert_tuples_to_dicts(post_subscribers)
            for subscriber in post_subscribers:
                subscriber['subscribed_on'] = self.utils.format_date_display(subscriber['subscribed_on'])
                subscriber['paid_amount'] = self.utils.format_currency(subscriber['paid_amount']/100)                
            response = self.create_response(post_subscribers,"UE011",addnl={"_total_rows":post_subscribers_count[0].count})
            return(response)
            
        response = self.create_error("UE012")
        return(response)

    def get_user_profile_subscribers_paid(self):        
        qparams = [self.session.get_session_value("_profile_seq"),
                   "%" if self.params["status"] == "ALL" else "ACTIVE"]

        profile_subscribers = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_SUBSCRIBERS_PAID",qparams,limit=self.is_paginated())
        if profile_subscribers:
            profile_subscribers_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_SUBSCRIBERS_PAID_COUNT",qparams)
            profile_subscribers = self.utils.convert_tuples_to_dicts(profile_subscribers)
            generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
            today = self.utils.get_today_date_str()
            for subscriber in profile_subscribers:  
                subscriber['from_date'] = self.utils.format_date_display(subscriber['from_date'])
                subscriber['to_date'] = self.utils.format_date_display(subscriber['to_date'])
                subscriber['paid_amount'] = self.utils.format_currency(subscriber['paid_amount']/100)
                subscriber["profile_picture"] = subscriber["profile_picture"] if not subscriber["profile_picture"] else  generic_code.create_media_url(SotrueAppConfig.profile_path + subscriber["profile_picture"],AppConfigs.s3_profiles_folder, subscriber["s3_enabled"])
                subscriber["cover_image"] = subscriber["cover_image"] if not subscriber["cover_image"] else  generic_code.create_media_url(SotrueAppConfig.profile_path + subscriber["cover_image"],AppConfigs.s3_profiles_folder, subscriber["s3_enabled"])
                if subscriber['status'] == "UNSUBS":
                    subscriber['status']= "Unsubscribed"
                elif subscriber['status'] == "ACTIVE" and self.utils.get_date_diff_days_str(subscriber['to_date'],today)>0:
                    subscriber['status']= "Expired"
                elif subscriber['status'] == "ACTIVE":
                    subscriber['status']= "Active"
                
            response = self.create_response(profile_subscribers,"UE011",addnl={"_total_rows":profile_subscribers_count[0].count})
            return(response)
            
        response = self.create_error("UE012")
        return(response)


    def report_issue(self):
        file_name = ""
        if self.params["uploads"] is not None:
            file_name = self.params["uploads"][0]["upload_name"]
            file_path = self.configs.temp_folder_path + file_name                    
            target_path = SotrueAppConfig.report_path + file_name  
            
            resize_extn = self.utils.resize_image(file_path)
            file_path = file_path[0:file_path.rfind(".")+1] + resize_extn
            target_path = target_path[0:target_path.rfind(".")+1] + resize_extn

            if AppConfigs.s3_enabled:
                file_name = self.utils.get_uuid_str() + self.params["uploads"][0]["upload_name"][self.params["uploads"][0]["upload_name"].rfind("."):]
                awsS3 = AppAwsS3()
                awsS3.send_media_file(file_path, file_name, AppConfigs.s3_others_folder)
            else:
                self.utils.copy_files(file_path,target_path) 
                self.cache_uploaded_file(self.params["uploads"][0]["key"],target_path)
            self.utils.delete_file(file_path)
            
        report_id = "R-" + self.utils.get_today_date_str() + "-";
        qparams = [self.params['post_seq'],self.params['reason_code'],self.params['comment'],self.session.get_session_value("_profile_seq"),
                   self.utils.get_cur_timestamp(),"UNRESOLVED",
                   "" if "link_existing" not in self.params else self.params["link_existing"],
                   "" if "link_stolen" not in self.params else self.params["link_stolen"],
                   file_name,report_id,self.params["source"],
                   "YES" if AppConfigs.s3_enabled else "NO"]
        report_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_REPORT_POST",qparams)
        self.tracker.track_report_post(self.get_post_user(self.params['post_seq']))

        report_id += str(report_seq)
        qparams = [report_id,report_seq]
        self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_REPORT_ID",qparams)
        
        qparams = [self.session.get_session_value("_user_seq")]
        email_id = self.connDB.execute_prepared_stmt("sotrueappuser","GET_EMAIL_ID",qparams)

        mailer = AppEmail()        
        if self.params["reason_code"] == "HELP_FEEDBACK":
            mailer.send_email(email_id[0].email_id,None,"Your Feeback For SoTrue",
                "Thank You! \n\nAt SoTrue, we’re committed to providing the best experience possible. Our team will review your input and get back to you as soon as we can.\n\nYour report reference number is: " + report_id + "\n\nBest,\nTeam SoTrue",                
                AppConfigs.email_sender);
            response = self.create_response(dict(report_seq = report_seq),"UE038")
            return(response)
        elif self.params["reason_code"] == "HELP_STOLEN":
            mailer.send_email(email_id[0].email_id,None,"SoTrue Incident Report",
                "Hey there!\n\nThank you for reporting this issue and helping us keep the community safe. Our team at SoTrue will review your report and respond to you shortly.\n\nYour report reference number is: " + report_id + "\n\nBest,\nTeam SoTrue\n",                
                "<EMAIL>");
            response = self.create_response(dict(report_seq = report_seq),"UE036")
            return(response)
        else:
            mailer.send_email(email_id[0].email_id,None,"Your Enquiry for SoTrue",
                "Hey there!\n\nThank you for reaching out to us. Our team at SoTrue will review your enquiry and get back to you shortly.\n\nYour enquiry reference number is: " + report_id + "\n\nBest,\nTeam SoTrue\n",                
                AppConfigs.email_sender);
            response = self.create_response(dict(report_seq = report_seq),"UE036")
            return(response)


    def block_account(self):
        if int(self.session.get_session_value("_profile_seq")) == int(self.params["block_profile_seq"]):
            response = self.create_error("UE048")
            return(response)

        qparams = [self.session.get_session_value("_profile_seq"),self.params["block_profile_seq"],'BLOCKED','ACTIVE']
        restrict_data = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_RESTRICTED_PROFILE",qparams)
        if restrict_data:
            qparams = [self.params["block_profile_seq"]]
            profile_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)
            response = self.create_error("UE046",subs=[profile_details[0].display_name])
            return(response)

        qparams = [self.session.get_session_value("_profile_seq"),self.params["block_profile_seq"],'RESTRICTED','ACTIVE']
        restrict_data = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_RESTRICTED_PROFILE",qparams)
        if restrict_data:
            qparams = ["BLOCKED",self.utils.get_cur_timestamp(),self.params['block_reason'],
                       self.params['comment'], restrict_data[0].restrict_seq]
            self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_RESTRICTED_PROFILE",qparams)
            qparams = [self.params["block_profile_seq"]]
            profile_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)
            response = self.create_response(dict(restrict_seq = restrict_data[0].restrict_seq),"UE047",subs=[profile_details[0].display_name])
            return(response)

        qparams = [self.params["block_profile_seq"],self.session.get_session_value("_profile_seq"),self.utils.get_cur_timestamp(),
                   "" if not "block_reason" in self.params else self.params["block_reason"],
                   "" if not "comment" in self.params else self.params["comment"],"BLOCKED","ACTIVE"]
        restrict_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_RESTRICTED_PROFILE",qparams)
        self.tracker.track_block_account(self.get_profile_user(self.params["block_profile_seq"]))

        qparams = [self.params["block_profile_seq"]]
        profile_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)
        response = self.create_response(dict(restrict_seq = restrict_seq),"UE047",subs=[profile_details[0].display_name])
        return(response)


    def restrict_account(self):
        if int(self.session.get_session_value("_profile_seq")) == int(self.params["restrict_profile_seq"]):
            response = self.create_error("UE048")
            return(response)
        qparams = [self.session.get_session_value("_profile_seq"),self.params["restrict_profile_seq"],'RESTRICTED','ACTIVE']
        restrict_data = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_RESTRICTED_PROFILE",qparams)
        if restrict_data:
            qparams = [self.params["restrict_profile_seq"]]
            profile_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)
            response = self.create_error("UE044",subs=[profile_details[0].display_name])
            return(response)
        qparams = [self.session.get_session_value("_profile_seq"),self.params["restrict_profile_seq"],'BLOCKED','ACTIVE']
        restrict_data = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_RESTRICTED_PROFILE",qparams)
        if restrict_data:
            qparams = [self.params["restrict_profile_seq"]]
            profile_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)
            response = self.create_error("UE046",subs=[profile_details[0].display_name])
            return(response)
        qparams = [self.params["restrict_profile_seq"],self.session.get_session_value("_profile_seq"),self.utils.get_cur_timestamp(),
                   "" if not "restrict_reason" in self.params else self.params["restrict_reason"],
                   "" if not "comment" in self.params else self.params["comment"],"RESTRICTED","ACTIVE"]
        restrict_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_RESTRICTED_PROFILE",qparams)
        self.tracker.track_restrict_account(self.get_profile_user(self.params["restrict_profile_seq"]))

        qparams = [self.params["restrict_profile_seq"]]
        profile_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)

        response = self.create_response(dict(restrict_seq = restrict_seq),"UE045",subs=[profile_details[0].display_name])
        return(response)


    def unblock_account(self):
         qparams = [self.utils.get_cur_timestamp(),'INACTIVE',self.params["restrict_seq"],self.params['block_profile_seq'],
                    self.session.get_session_value("_profile_seq")]
         self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_RESTRICTED_PROFILE_STATUS",qparams)
         self.tracker.track_unblock_account(self.get_profile_user(self.params['block_profile_seq']))

         response = self.create_response(dict(),"UE051")
         return(response)


    def unrestrict_account(self):
         qparams = [self.utils.get_cur_timestamp(),'INACTIVE',self.params["restrict_seq"],self.params['restrict_profile_seq'],
                    self.session.get_session_value("_profile_seq")]
         self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_RESTRICTED_PROFILE_STATUS",qparams)
         self.tracker.track_unrestrict_account(self.get_profile_user(self.params['restrict_profile_seq']))

         response = self.create_response(dict(),"UE052")
         return(response)


    def get_blocked_accounts(self):
        qparams = [self.session.get_session_value("_profile_seq"),'BLOCKED','ACTIVE']
        blocked_list = self.connDB.execute_prepared_stmt("sotrueappuser","GET_RESTRICTED_PROFILES",qparams)
        if not blocked_list:
            response = self.create_error("UE049")
            return(response)
        blocked_list = self.utils.convert_tuples_to_dicts(blocked_list)
        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
        for blocked in blocked_list:
            blocked['profile_picture'] = blocked['profile_picture'] if not blocked['profile_picture'] else generic_code.create_media_url(SotrueAppConfig.profile_path + blocked['profile_picture'],AppConfigs.s3_profiles_folder,blocked['s3_enabled'])
            blocked['cover_image'] = blocked['cover_image'] if not blocked['cover_image'] else generic_code.create_media_url(SotrueAppConfig.profile_path + blocked['cover_image'],AppConfigs.s3_profiles_folder,blocked['s3_enabled'])
            blocked['restricted_on'] = self.utils.format_ts_display(blocked['restricted_on'])

        response = self.create_response(blocked_list,"UE011")
        return(response)


    def get_restrcited_accounts(self):
        qparams = [self.session.get_session_value("_profile_seq"),'RESTRICTED','ACTIVE']
        restricted_list = self.connDB.execute_prepared_stmt("sotrueappuser","GET_RESTRICTED_PROFILES",qparams)
        if not restricted_list:
            response = self.create_error("UE050")
            return(response)
        restricted_list = self.utils.convert_tuples_to_dicts(restricted_list)
        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
        for restricted in restricted_list:
            restricted['profile_picture'] = restricted['profile_picture'] if not restricted['profile_picture'] else generic_code.create_media_url(SotrueAppConfig.profile_path + restricted['profile_picture'],AppConfigs.s3_profiles_folder,restricted['s3_enabled'])
            restricted['cover_image'] = restricted['cover_image'] if not restricted['cover_image'] else generic_code.create_media_url(SotrueAppConfig.profile_path + restricted['cover_image'],AppConfigs.s3_profiles_folder,restricted['s3_enabled'])
            restricted['restricted_on'] = self.utils.format_ts_display(restricted['restricted_on'])

        response = self.create_response(restricted_list,"UE011")
        return(response)


    def delete_account(self):
        status = "INACTIVE"
        if self.params["delete_type"] == "DELETE":
            status = "INACTIVE" #DELETED

        qparams=[status,self.session.get_session_value("_profile_seq")]
        self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_PROFILE_STATUS",qparams)
        qparams=[status,"",
                 ("DELETE," + self.params["delete_reason"]) if self.params["delete_type"] == "DELETE" else ("INACTIVE," + self.params["delete_reason"]),
                 self.params["delete_comments"],
                    self.params["other_reason"] if "other_reason" in self.params else None,
                    self.session.get_session_value("_user_seq")]
        self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_DELETE_STATUS",qparams)
        self.deactivate_user_data("INACTIVE",self.session.get_session_value("_profile_seq"))
       
        self.tracker.track_delete_account()

        qparams = [self.session.get_session_value("_user_seq")]
        user_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_DETAILS",qparams)
        mailer = AppEmail()
        mailer.send_email(user_details[0].email_id,None,
                          SotrueAppConfig.test_server_msg + "SoTrue User Account " + ("deletion" if self.params["delete_type"] == "DELETE" else "de-activation"),
                "Hey " + user_details[0].full_name + ",\n\nWe’ve received your request to deactivate your account and would like to understand your reasons. Your feedback is crucial to us as we strive to improve our services and better meet your needs.\n\n" + 
                "We appreciate you taking a few minutes to share your experience with us. Rest assured, your feedback will be kept confidential and used solely for service improvement purposes.\n\n" + 
                "Please note that account deactivation is permanent and can only be reversed by contacting us with a detailed request to reactivate.\n\n" + 
                "If you have any questions or concerns, feel free to reach out. Thank you for your time and input.\n\nBest,\nTeam SoTrue\n");
        mailer.send_email("<EMAIL>",None,
                          SotrueAppConfig.test_server_msg + "SoTrue User Account " + ("deletion" if self.params["delete_type"] == "DELETE" else "de-activation"),
                          "\Hello,\n\nThe following user has requested to " + ("DELETE" if self.params["delete_type"] == "DELETE" else "DE-ACTIVATE") + " their SoTrue account:\n\n" +\
                              "Name: " + user_details[0].full_name+"\n"\
                              "Email: " + user_details[0].email_id+"\n"\
                              "Mobile: " + (user_details[0].mobile_number if user_details[0].mobile_number else "-") + 
                                "\n\nBest,\nTeam SoTrue")

        response = self.create_response(dict(),"UE039")
        return(response)


    def restore_account(self): 
        qparams = [self.params["profile_seq"]]
        user_seq = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_USER_SEQ",qparams)

        qparams=["ACTIVE",self.params["profile_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_PROFILE_STATUS",qparams)
        qparams=["ACTIVE","",user_seq[0].user_seq]
        self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_USER_STATUS",qparams)
        self.deactivate_user_data("ACTIVE",self.params["profile_seq"])               
        
        qparams = [user_seq[0].user_seq]
        user_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_DETAILS",qparams)
        mailer = AppEmail()
        mailer.send_email(user_details[0].email_id,None,
                          SotrueAppConfig.test_server_msg + "SoTrue User Account re-activation",
                "\nDear " + user_details[0].full_name + ",\n\n" +\
                "We’re excited to let you know that your SoTrue account has been successfully reactivated. You can now log in and access all the features and benefits our platform has to offer.\n\n" + 
                "If you run into any issues or need assistance, please don’t hesitate to reach out to our support <NAME_EMAIL>. We're here to help ensure you have a great experience on SoTrue.\n\n" + 
                "Thank you for your continued support. We’re thrilled to have you back with us!\n\n" + 
                "Welcome back to the SoTrue family!\n\nBest,\nTeam SoTrue");

        response = self.create_response(dict(),"AE023")
        return(response)


    def upload_account_verification(self):
        qparams = [self.session.get_session_value("_profile_seq")]
        verification_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_VERIFICATION_DETAILS",qparams)
        if verification_data and verification_data[0].verified_status!='REJECTED':
            if verification_data[0].verified_status=='VERIFIED':
                response = self.create_error("UE043")
                return(response)
            elif verification_data[0].verified_status=='PENDING':
                response = self.create_error("UE042")
                return(response)

        verify_image = None
        docu_image = None
        combi_image = None
        if self.params["uploads"] is not None:
            if self.params["uploads"][0]["key"] == "verification_image":
                verify_image = self.params["uploads"][0]["upload_name"]
            elif self.params["uploads"][0]["key"] == "document_image":
                docu_image = self.params["uploads"][0]["upload_name"]
            elif self.params["uploads"][0]["key"] == "combined_image":
                combi_image = self.params["uploads"][0]["upload_name"]

            if len(self.params["uploads"]) > 1:
                if self.params["uploads"][1]["key"] == "verification_image":
                    verify_image = self.params["uploads"][1]["upload_name"]
                elif self.params["uploads"][1]["key"] == "document_image":
                    docu_image = self.params["uploads"][1]["upload_name"]
                elif self.params["uploads"][1]["key"] == "combined_image":
                    combi_image = self.params["uploads"][1]["upload_name"]

            if len(self.params["uploads"]) > 2:
                if self.params["uploads"][2]["key"] == "verification_image":
                    verify_image = self.params["uploads"][2]["upload_name"]
                elif self.params["uploads"][2]["key"] == "document_image":
                    docu_image = self.params["uploads"][2]["upload_name"]
                elif self.params["uploads"][2]["key"] == "combined_image":
                    combi_image = self.params["uploads"][2]["upload_name"]

            if not verify_image or not docu_image:
                response = self.create_error("UE041")
                return(response)
            
            if verify_image:
                resize_extn = self.utils.resize_image(self.configs.temp_folder_path + verify_image)
                verify_image = verify_image[0:verify_image.rfind(".")+1] + resize_extn
                if AppConfigs.s3_enabled:
                    file_name = self.utils.get_uuid_str() + verify_image[verify_image.rfind("."):]
                    awsS3 = AppAwsS3()
                    awsS3.send_media_file(self.configs.temp_folder_path + verify_image, file_name, AppConfigs.s3_accounts_folder)
                    verify_image = file_name
                else:
                    self.utils.copy_files(self.configs.temp_folder_path + verify_image,SotrueAppConfig.verification_docs + verify_image)            
                    self.cache_uploaded_file("verification_image",SotrueAppConfig.verification_docs + verify_image)
                self.utils.delete_file(self.configs.temp_folder_path + verify_image)

            if docu_image:
                resize_extn = self.utils.resize_image(self.configs.temp_folder_path + docu_image)
                docu_image = docu_image[0:docu_image.rfind(".")+1] + resize_extn
                if AppConfigs.s3_enabled:
                    file_name = self.utils.get_uuid_str() + docu_image[docu_image.rfind("."):]
                    awsS3 = AppAwsS3()
                    awsS3.send_media_file(self.configs.temp_folder_path + docu_image, file_name, AppConfigs.s3_accounts_folder)
                    docu_image = file_name
                else:                
                    self.utils.copy_files(self.configs.temp_folder_path + docu_image,SotrueAppConfig.verification_docs + docu_image)            
                    self.cache_uploaded_file("document_image",SotrueAppConfig.verification_docs + docu_image)
                self.utils.delete_file(self.configs.temp_folder_path + docu_image)
            
            if combi_image:
                resize_extn = self.utils.resize_image(self.configs.temp_folder_path + combi_image)
                combi_image = combi_image[0:combi_image.rfind(".")+1] + resize_extn
                if AppConfigs.s3_enabled:
                    file_name = self.utils.get_uuid_str() + combi_image[combi_image.rfind("."):]
                    awsS3 = AppAwsS3()
                    awsS3.send_media_file(self.configs.temp_folder_path + combi_image, file_name, AppConfigs.s3_accounts_folder)
                    combi_image = file_name
                else:
                    self.utils.copy_files(self.configs.temp_folder_path + combi_image,SotrueAppConfig.verification_docs + combi_image)            
                    self.cache_uploaded_file("combined_image",SotrueAppConfig.verification_docs + combi_image)
                self.utils.delete_file(self.configs.temp_folder_path + combi_image)            

            qparams = [self.session.get_session_value("_profile_seq"), verify_image, docu_image,combi_image,
                       self.utils.get_cur_timestamp(),"PENDING",self.params["type"],
                       "YES" if AppConfigs.s3_enabled else "NO",
                       self.params["docu_type"], self.params["playlist_prog"]]
            verification_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_PROFILE_VERIFICATION",qparams)

            qparams = ['INACTIVE',self.session.get_session_value("_profile_seq"),'VERIFY_FAILED']
            self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_ALERT_STATUS",qparams)

            response = self.create_response(dict(verification_seq=verification_seq),"UE040")
            return(response);

        response = self.create_error("UE041")
        return(response)


    def upload_account_verification_firm(self):
        qparams = [self.session.get_session_value("_profile_seq")]
        verification_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_VERIFICATION_DETAILS",qparams)
        if verification_data and verification_data[0].verified_status!='REJECTED':
            if verification_data[0].verified_status=='VERIFIED':
                response = self.create_error("UE043")
                return(response)
            elif verification_data[0].verified_status=='PENDING':
                response = self.create_error("UE042")
                return(response)

        qparams = [self.session.get_session_value("_profile_seq"),self.utils.get_cur_timestamp(),"PENDING",
                   self.params["type"],self.params["gstn"],self.params["pan"],self.params["playlist_prog"]]
        verification_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_PROFILE_VERIFICATION_FIRM",qparams)

        qparams = ['INACTIVE',self.session.get_session_value("_profile_seq"),'VERIFY_FAILED']
        self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_ALERT_STATUS",qparams)

        qparams = [self.session.get_session_value("_user_seq")]
        user_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_DETAILS",qparams)
        mailer = AppEmail()
        mailer.send_email(SotrueAppConfig.accounts_email,None,
                          SotrueAppConfig.test_server_msg + "SoTrue User Account Verification Details",
                "Hey there!\n\nThe following details have been submitted for account verification\n\n" +
                      "Email Id: " + user_details[0].email_id + "\n" +
                      "Full Name: " + user_details[0].full_name + "\n\n" +
                      "GSTIN: " + self.params["gstn"] + "\n" +
                      "PAN: " + self.params["pan"] + "\n\n" +                      
                      "Thank you for providing this information\nBest,\nTeam SoTrue");

        response = self.create_response(dict(verification_seq=verification_seq),"UE040")
        return(response);


    def get_notifications_likes(self):
        qparams = [self.utils.get_ts_back_by(self.utils.get_cur_timestamp(),24*7),self.session.get_session_value("_profile_seq")]
        notifications = self.connDB.execute_prepared_stmt("sotrueappuser","GET_LIKE_NOTIFICATIONS",qparams,limit=self.is_paginated()) 

        if not notifications:
            response = self.create_error("UE053")
            return(response)

        profile_list = []
        for rec in notifications:
            profile_list.append(rec.liked_by_profile)

        subs = {"<PROFILE_LIST>":self.utils.convert_to_delim_str(profile_list,",")}
        profile_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_FOR_USERS",None,subs=subs)
        profile_data_indexed = dict()
        for row in profile_data:
            profile_data_indexed[row.profile_seq] = row

        notifications = self.utils.convert_tuples_to_dicts(notifications)
        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
        for rec in notifications:
            if rec["liked_by_profile"] in profile_data_indexed:             
                rec["profile_picture"] = profile_data_indexed[rec["liked_by_profile"]].profile_picture if not profile_data_indexed[rec["liked_by_profile"]].profile_picture else  generic_code.create_media_url(SotrueAppConfig.profile_path + profile_data_indexed[rec["liked_by_profile"]].profile_picture,AppConfigs.s3_profiles_folder,profile_data_indexed[rec["liked_by_profile"]].s3_enabled)
                rec["cover_image"] = profile_data_indexed[rec["liked_by_profile"]].cover_image if not profile_data_indexed[rec["liked_by_profile"]].cover_image else  generic_code.create_media_url(SotrueAppConfig.profile_path + profile_data_indexed[rec["liked_by_profile"]].cover_image,AppConfigs.s3_profiles_folder,profile_data_indexed[rec["liked_by_profile"]].s3_enabled)
                rec["user_handle"] = profile_data_indexed[rec["liked_by_profile"]].user_handle
                rec["display_name"] = profile_data_indexed[rec["liked_by_profile"]].display_name
                rec["is_verified"] = profile_data_indexed[rec["liked_by_profile"]].is_verified
            else:
                rec["profile_picture"] = None
                rec["cover_image"] = None
                rec["user_handle"] = "-"
                rec["display_name"] = "-"
                rec["is_verified"] = "NO"
            rec["like_time"] = self.utils.get_formatted_time_past(rec["like_time"])
            rec["media_file"] = generic_code.create_media_url(SotrueAppConfig.media_path + str(self.session.get_session_value("_profile_seq")) + "/" + rec["media_file"],AppConfigs.s3_posts_folder,rec["s3_enabled"])
            rec["media_cover"] = rec["media_cover"] if not rec["media_cover"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(self.session.get_session_value("_profile_seq")) + "/" + rec["media_cover"],AppConfigs.s3_posts_folder,rec["s3_enabled"])

        response = self.create_response(notifications,"UE011")
        return(response);


    def get_notifications_comments(self):
        qparams = ['POST','ACTIVE',self.session.get_session_value("_profile_seq"),self.utils.get_ts_back_by(self.utils.get_cur_timestamp(),24*7)]
        comment_list = self.connDB.execute_prepared_stmt("sotrueappuser","GET_COMMENT_NOTIFICATIONS",qparams,limit=self.is_paginated())

        if not comment_list:
            response = self.create_error("UE054")
            return(response)

        profile_list = []
        for rec in comment_list:
            profile_list.append(rec.comment_profile_seq)

        subs = {"<PROFILE_LIST>":self.utils.convert_to_delim_str(profile_list,",")}
        profile_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_FOR_USERS",None,subs=subs)
        profile_data_indexed = dict()
        for row in profile_data:
            profile_data_indexed[row.profile_seq] = row

        comment_list = self.utils.convert_tuples_to_dicts(comment_list)
        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
        for rec in comment_list: 
            if rec["comment_profile_seq"] in profile_data_indexed:             
                rec["profile_picture"] = profile_data_indexed[rec["comment_profile_seq"]].profile_picture if not profile_data_indexed[rec["comment_profile_seq"]].profile_picture else  generic_code.create_media_url(SotrueAppConfig.profile_path + profile_data_indexed[rec["comment_profile_seq"]].profile_picture,AppConfigs.s3_profiles_folder,profile_data_indexed[rec["comment_profile_seq"]].s3_enabled)
                rec["cover_image"] = profile_data_indexed[rec["comment_profile_seq"]].cover_image if not profile_data_indexed[rec["comment_profile_seq"]].cover_image else  generic_code.create_media_url(SotrueAppConfig.profile_path + profile_data_indexed[rec["comment_profile_seq"]].cover_image,AppConfigs.s3_profiles_folder, profile_data_indexed[rec["comment_profile_seq"]].s3_enabled)
                rec["user_handle"] = profile_data_indexed[rec["comment_profile_seq"]].user_handle
                rec["display_name"] = profile_data_indexed[rec["comment_profile_seq"]].display_name
                rec["is_verified"] = profile_data_indexed[rec["comment_profile_seq"]].is_verified
            else:
                rec["profile_picture"] = None
                rec["cover_image"] = None
                rec["user_handle"] = "-"
                rec["display_name"] = "-"
                rec["is_verified"] = "NO"
            rec["comment_time"] = self.utils.get_formatted_time_past(rec["comment_time"])
            rec["media_file"] = generic_code.create_media_url(SotrueAppConfig.media_path + str(self.session.get_session_value("_profile_seq")) + "/" + rec["media_file"],AppConfigs.s3_posts_folder, rec["s3_enabled"])
            rec["media_cover"] = rec["media_cover"] if not rec["media_cover"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(self.session.get_session_value("_profile_seq")) + "/" + rec["media_cover"],AppConfigs.s3_posts_folder, rec["s3_enabled"])

        response = self.create_response(comment_list,"UE011")
        return(response);


    def get_notifications_subscribes(self):
        qparams = [self.session.get_session_value("_profile_seq")]
        profile_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)

        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger) 
        if profile_details[0].type == "PAID":
            qparams = [self.session.get_session_value("_profile_seq"),'ACTIVE',self.utils.get_ts_back_by(self.utils.get_cur_timestamp(),24*7),]
            subs_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_SUBSCRIBE_NOTIFICATIONS",qparams)
            if not subs_details:
                response = self.create_error("UE055")
                return(response)
            subs_details = self.utils.convert_tuples_to_dicts(subs_details)
            for rec in subs_details:
                rec["profile_picture"] = rec["profile_picture"] if not rec["profile_picture"] else  generic_code.create_media_url(SotrueAppConfig.profile_path + rec["profile_picture"],AppConfigs.s3_profiles_folder, rec["s3_enabled"])
                rec["cover_image"] = rec["cover_image"] if not rec["cover_image"] else  generic_code.create_media_url(SotrueAppConfig.profile_path + rec["cover_image"],AppConfigs.s3_profiles_folder, rec["s3_enabled"])
                rec["paid_on"] = self.utils.get_formatted_time_past(rec["paid_on"])
                rec["type"] = "PROFILE"
            response = self.create_response(subs_details,"UE011")
            return(response);

        else:
            qparams = [self.session.get_session_value("_profile_seq"),self.utils.get_ts_back_by(self.utils.get_cur_timestamp(),24*7),'ACTIVE']
            subs_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_SUBSCRIBE_NOTIFICATIONS",qparams)
            if not subs_details:
                response = self.create_error("UE055")
                return(response)
            subs_details = self.utils.convert_tuples_to_dicts(subs_details)
            for rec in subs_details:
                rec["profile_picture"] = rec["profile_picture"] if not rec["profile_picture"] else  generic_code.create_media_url(SotrueAppConfig.profile_path + rec["profile_picture"],AppConfigs.s3_profiles_folder, rec["s3_profile"])
                rec["cover_image"] = rec["cover_image"] if not rec["cover_image"] else  generic_code.create_media_url(SotrueAppConfig.profile_path + rec["cover_image"],AppConfigs.s3_profiles_folder, rec["s3_profile"])
                rec["subscribed_on"] = self.utils.get_formatted_time_past(rec["subscribed_on"])
                rec["media_file"] = generic_code.create_media_url(SotrueAppConfig.media_path + str(self.session.get_session_value("_profile_seq")) + "/" + rec["media_file"],AppConfigs.s3_posts_folder, rec["s3_post"])
                rec["media_cover"] =  rec["media_cover"] if not rec["media_cover"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(self.session.get_session_value("_profile_seq")) + "/" + rec["media_cover"],AppConfigs.s3_posts_folder, rec["s3_post"])
                rec["type"] = "POST"
            response = self.create_response(subs_details,"UE011")
            return(response);


    def get_notifications_follows(self):
        qparams = [self.session.get_session_value("_profile_seq"),self.utils.get_ts_back_by(self.utils.get_cur_timestamp(),24*7)]
        follow_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_FOLLOW_NOTIFICATIONS",qparams)
        if not follow_details:
            response = self.create_error("UE083")
            return(response)
        follow_details = self.utils.convert_tuples_to_dicts(follow_details)
        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger) 
        for rec in follow_details:
            rec["profile_picture"] = rec["profile_picture"] if not rec["profile_picture"] else  generic_code.create_media_url(SotrueAppConfig.profile_path + rec["profile_picture"],AppConfigs.s3_profiles_folder, rec["s3_enabled"])
            rec["cover_image"] = rec["cover_image"] if not rec["cover_image"] else  generic_code.create_media_url(SotrueAppConfig.profile_path + rec["cover_image"],AppConfigs.s3_profiles_folder,  rec["s3_enabled"])
            rec["followed_on"] = self.utils.get_formatted_time_past(rec["followed_on"])            
        response = self.create_response(follow_details,"UE011")
        return(response);


    def get_payment_summary_values(self):        
        from_date = None        
        today = self.utils.get_today_date_str()
        to_date = self.utils.retard_date_by(today,1) + " 23:59:59"       
        if self.params["date_range"] == "CURRENT_DAY_USER":
            from_date = today + " 00:00:00"
            to_date = today + " 23:59:59"           
        elif self.params["date_range"] == "PREV_DAY_USER":            
            from_date = self.utils.retard_date_by(today,1) + " 00:00:00"              
        elif self.params["date_range"] == "PAST_7_DAYS_USER":
            from_date = self.utils.retard_date_by(today,7) + " 00:00:00"            
        elif self.params["date_range"] == "PAST_15_DAY_USER":
            from_date = self.utils.retard_date_by(today,15) + " 00:00:00"            
        elif self.params["date_range"] == "PAST_30_DAY_USER":
            from_date = self.utils.retard_date_by(today,30) + " 00:00:00"            
        elif self.params["date_range"] == "PAST_60_DAY_USER":
            from_date = self.utils.retard_date_by(today,60) + " 00:00:00"           
        elif self.params["date_range"] == "PAST_90_DAY_USER":
            from_date = self.utils.retard_date_by(today,90) + " 00:00:00"  
        elif self.params["date_range"] == "PAST_180_DAY_USR":
            from_date = self.utils.retard_date_by(today,180) + " 00:00:00"  
        elif self.params["date_range"] == "LIFETIME_USER":
            from_date = "2021-01-01 00:00:00"
            to_date = today + " 23:59:59"            

        qparams = [self.session.get_session_value("_user_seq"),from_date,to_date]  
        referals_earned = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PAYMENTS_RECEIVED_FOR_REFERRALS",qparams)  

        qparams = [self.session.get_session_value("_profile_seq")]
        profile_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)
                        
        qparams = [self.session.get_session_value("_profile_seq"),from_date,to_date]  
        payments_made = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PAYMENTS_MADE",qparams)            
        payments_received_profile = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PAYMENTS_RECEIVED_FOR_PROFILE",qparams)   
        payments_received_posts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PAYMENTS_RECEIVED_FOR_POSTS",qparams)
        payments_received = payments_received_profile[0].sum if payments_received_profile[0].sum else 0
        payments_received += payments_received_posts[0].sum if payments_received_posts[0].sum else 0        
        response = [{"payments_made":self.utils.format_currency(payments_made[0].sum/100 if payments_made[0].sum else 0),
                     "payments_received":self.utils.format_currency(payments_received/100),
                     "referral_payment":self.utils.format_currency(referals_earned[0].sum/100 if referals_earned[0].sum else 0)}]
        response = self.create_response(response,"UE011")
        return(response);


    def get_alert_settings(self):
        qparams = [self.session.get_session_value("_profile_seq")]
        alert_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_ALERT_SETTINGS",qparams)
        alert_data = self.utils.convert_tuples_to_dicts(alert_data)
        response = self.create_response(alert_data,"UE011")
        return(response);


    def update_alert_settings(self):
        qparams = [self.params["email_alert"],self.session.get_session_value("_profile_seq")]
        self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_ALERT_SETTINGS",qparams)
        response = self.create_response(dict(),"UE015")
        return(response);


    def get_user_messages(self):
        qparams = [self.session.get_session_value("_profile_seq"),'ACTIVE']
        alert = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_ALERTS",qparams)

        if not alert:
            response = self.create_error("UE012")
            return(response)

        alert = self.utils.convert_tuples_to_dicts(alert)
        response = self.create_response(alert,"UE011")
        return(response);


    def update_message_status(self):
        qparams = ['INACTIVE','YES',self.params["alert_seq"],self.session.get_session_value("_profile_seq")]
        self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_ALERT_DISPLAY_STATUS",qparams)

        response = self.create_response(dict(),"UE015")
        return(response);


    def check_user_handle(self):
        qparams = [self.params["user_handle"],self.session.get_session_value("_profile_seq")]
        handle_data = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_USER_HANDLE",qparams)

        if handle_data:
            response = self.create_error("UE056")
            return(response)
        elif not self.check_remote_duplicate_user(None,self.params["user_handle"]):
            response = self.create_error("UE056")
            return(response)
        response = self.create_response(dict(),"UE012")
        return(response);


    def get_user_server(self):
        if "user_id" in self.params:
            qparams = [self.params["user_id"]]
            user_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_CREDENTIALS",qparams)
            if not user_data:
                response = self.create_error("UE061")
                return(response)
            if user_data[0].server in SotrueAppConfig.allowed_servers:
                response = self.create_response(dict(server=SotrueAppConfig.servers[user_data[0].server]),"UE011")
            else:
                response = self.create_response(dict(server=SotrueAppConfig.servers[SotrueAppConfig.allowed_servers[0]]),"UE011")
            return(response);
        else:
            server_dist = self.connDB.execute_prepared_stmt("sotrueappuser","GET_SERVER_DISTRIBUTION",None)
            missing_server = ""
            for server in SotrueAppConfig.allowed_servers:
                found = False
                for qserver in server_dist:
                    if server == qserver.server:
                        found = True
                        break
                if not found:
                    missing_server = server
                    break
            least_server = ""
            if not missing_server:                
                least_count = 9223372036854775807
                for server in server_dist:
                    if server.count < least_count and server.server in SotrueAppConfig.allowed_servers:
                        least_count = server.count
                        least_server = server.server
                if not least_server:
                    least_server = SotrueAppConfig.allowed_servers[0]
            else:
                least_server = missing_server

            response = self.create_response(dict(server=SotrueAppConfig.servers[least_server]),"UE011")
            return(response);

    #
    # Checks for a duplicate user in the other servers - True if there is NO duplicate user
    #
    def check_remote_duplicate_user(self,email=None,handle=None): 
        try:
            payload = {"user_id":SotrueAppConfig.remote_servers[0]["user_id"],
                        "password":SotrueAppConfig.remote_servers[0]["password"],
                        "_action_code":"ADMIN_LOGIN"}
            json_data = self.utils.to_json(payload)
            response = requests.post(SotrueAppConfig.remote_servers[0]["url"],data=json_data,headers={'content-type': 'application/json'})
            access_key = None            
            if response.status_code == 200:
                json_data = self.utils.parse_json(response.text)
                if json_data["status"] == "SUCCESS":
                    access_key=json_data["_access_key"]                
                else:
                    return True
            else:
                return True

            payload = {"user_id":SotrueAppConfig.remote_servers[0]["user_id"],
                        "password":SotrueAppConfig.remote_servers[0]["password"],
                        "_action_code":"CHECK_REMOTE_DUPLICATE_USER",
                        "_access_key":access_key,
                        "email":"","handle":"",
                        "db_key":"sotruedb",
                        "db_schema":"sotrueschema"}
            if email:
                payload["email"]=email
            elif handle:
                payload["handle"]=handle

            json_data = self.utils.to_json(payload)
            response = requests.post(SotrueAppConfig.remote_servers[0]["url"],data=json_data,headers={'content-type': 'application/json'})            
            if response.status_code == 200:
                json_data = self.utils.parse_json(response.text)
                if json_data["status"] == "SUCCESS":
                    return False
                else:
                    return True
            else:
                return True
        except Exception as e:
            self.logger.log_message(e)
            return True


    def check_remote_duplicate_user_test(self):
        if self.params["email"]:
            if self.check_remote_duplicate_user(self.params["email"],None):
                response = self.create_response(dict(),"UE012")
                return(response)
        elif self.params["handle"]:
            if self.check_remote_duplicate_user(None,self.params["handle"]):
                response = self.create_response(dict(),"UE012") 
                return(response)
        response = self.create_error("UE011")
        return(response)


    def update_user_location(self):
        qparams = [self.params["country"],self.params["state"],self.session.get_session_value("_user_seq")]
        self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_USER_LOCATION",qparams)
        response = self.create_response(dict(),"UE015")
        return(response)


    def get_user_referral_code(self):
        qparams = [self.session.get_session_value("_profile_seq")]
        referral_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_REFERRAL_CODE",qparams)

        if referral_data[0].referral_code:
            response = self.utils.convert_tuples_to_dicts(referral_data)        
            response = self.create_response(response,"UE011") 
            return(response)
        else:
            code = AppSecurity.get_random_password()
            qparams = [code]
            duplicate = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_DUPLICATE_CODE",qparams)
            while duplicate:
                code = AppSecurity.get_random_password()
                qparams = [code]
                duplicate = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_DUPLICATE_CODE",qparams)
            qparams = [code,self.session.get_session_value("_profile_seq")]
            self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_REFERAL_CODE",qparams)
            qparams = [self.session.get_session_value("_profile_seq")]
            referral_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_REFERRAL_CODE",qparams)
            response = self.utils.convert_tuples_to_dicts(referral_data)  
            response = self.create_response(response,"UE011") 
            return(response)


    def get_referral_earned_users(self):
        qparams = [self.session.get_session_value("_user_seq")]
        referral_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_REFERRAL_EARNED_USERS",qparams)
        if not referral_data:
            response = self.create_error("UE072")
            return(response)
        referral_data_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_REFERRAL_EARNED_USERS_COUNT",qparams)
        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)     
        referral_data = self.utils.convert_tuples_to_dicts(referral_data)
        for referral in referral_data:
            referral['used_on'] = self.utils.format_date_display(referral['used_on'])                
            referral["profile_picture"] = referral["profile_picture"] if not referral["profile_picture"] else generic_code.create_media_url(SotrueAppConfig.profile_path + referral["profile_picture"],AppConfigs.s3_profiles_folder, referral["s3_enabled"])
            referral["cover_image"] = referral["cover_image"] if not referral["cover_image"] else generic_code.create_media_url(SotrueAppConfig.profile_path + referral["cover_image"],AppConfigs.s3_profiles_folder, referral["s3_enabled"])          
        response = self.create_response(referral_data,"UE011",addnl={"_total_rows":referral_data_count[0].count}) 
        return(response)


    def get_payout_details(self):
        monthly_payments = []
        qparams = [self.session.get_session_value("_user_seq"),'PENDING']
        pending_requests = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PAYOUT_REQUESTS",qparams)
        if pending_requests:
            for rec in pending_requests:
               monthly_payments.append({"month":rec.requested_month,
                                         "display_month":self.utils.convert_month_name(rec.requested_month),
                                         "total":self.utils.format_currency(rec.total_payout/100),
                                         "subscription":self.utils.format_currency(rec.subs_payout/100),
                                         "referal":self.utils.format_currency(rec.referral_payout/100),
                                         "status":"PENDING"})

        qparams = [self.session.get_session_value("_user_seq")]
        last_payout = self.connDB.execute_prepared_stmt("sotrueappuser","GET_LAST_PAYOUT",qparams)
        last_month = None
        if last_payout and last_payout[0].last_payout:
            last_month = last_payout[0].last_payout
            last_month = self.utils.incr_month(last_month)
        if not last_month:
            qparams = [self.session.get_session_value("_profile_seq")]
            post_payout = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_POST_EARNINGS",qparams)
            profile_payout = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_PROFILE_EARNINGS",qparams)
            referral_payout = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_REFERRAL_EARNINGS",qparams)
            if post_payout and post_payout[0].first_pay:
                last_month = post_payout[0].first_pay
            if profile_payout and profile_payout[0].first_pay:
                if not last_month:
                    last_month = profile_payout[0].first_pay
                elif self.utils.date_diff_hrs(last_month,profile_payout[0].first_pay) < 0:
                    last_month = profile_payout[0].first_pay
            if referral_payout and referral_payout[0].first_pay:
                if not last_month:
                    last_month = referral_payout[0].first_pay
                elif self.utils.date_diff_hrs(last_month,referral_payout[0].first_pay) < 0:
                    last_month = referral_payout[0].first_pay
        if not last_month:
            response = self.create_error("UE068")
            return(response)

        month_range = self.utils.get_year_months(last_month)        
        cur_month = self.utils.get_cur_year_month()
        for month in month_range:
            if month == cur_month:
                break
            from_date = month + "-01 00:00:00"
            to_date = self.utils.incr_month(from_date) + "-01 00:00:00"
            qparams = [self.session.get_session_value("_profile_seq"), from_date, to_date]
            post_earnings = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_EARNINGS",qparams)
            profile_earnings = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_EARNINGS",qparams)
            qparams = [self.session.get_session_value("_user_seq"), from_date, to_date]
            referal_earnings = self.connDB.execute_prepared_stmt("sotrueappuser","GET_REFERRAL_EARNINGS",qparams)

            total_month = 0
            ref_earning = 0
            sub_earning = 0
            if post_earnings and post_earnings[0].amount_earned:
                total_month = post_earnings[0].amount_earned
                sub_earning = post_earnings[0].amount_earned
            if profile_earnings and profile_earnings[0].amount_earned:
                total_month += profile_earnings[0].amount_earned
                sub_earning += profile_earnings[0].amount_earned
            if referal_earnings and referal_earnings[0].amount_earned:
                total_month += referal_earnings[0].amount_earned
                ref_earning = referal_earnings[0].amount_earned

            if total_month:
                monthly_payments.append({"month":month,
                                         "display_month":self.utils.convert_month_name(month),
                                         "total":self.utils.format_currency(total_month/100),
                                         "subscription":self.utils.format_currency(sub_earning/100),
                                         "referal":self.utils.format_currency(ref_earning/100),
                                         "status":"NEW"})        
        if not monthly_payments:
            response = self.create_error("UE069")
            return(response)
       
        qparams = [self.session.get_session_value("_user_seq")]
        gstin = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_GSTIN",qparams)        
        response = self.create_response(monthly_payments,"UE011",
                            addnl={"gstn":"0" if not gstin[0].gstin else gstin[0].gstin})
        return(response)


    def get_payout_history(self):
        qparams = [self.session.get_session_value("_user_seq"),'SETTLED']
        history_requests = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PAYOUT_REQUESTS_HIST",qparams,limit=self.is_paginated())

        if not history_requests:
            response = self.create_error("UE071")
            return(response)
       
        request_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PAYOUT_REQUESTS_HIST_COUNT",qparams)
        monthly_payments = []
        for rec in history_requests:
             monthly_payments.append({"month":rec.requested_month,
                                         "display_month":self.utils.convert_month_name(rec.requested_month),
                                         "total":self.utils.format_currency(rec.total_payout/100),
                                         "subscription":self.utils.format_currency(rec.subs_payout/100),
                                         "referal":self.utils.format_currency(rec.referral_payout/100),
                                         "status":"SETTLED",
                                         "settled_on":self.utils.convert_month_name(rec.settled_on)})
        response = self.create_response(monthly_payments,"UE011",addnl={"_total_rows":request_count[0].count})
        return(response)


    def submit_payout_request(self):
        pay_out_months = self.utils.parse_json(self.params["payout_months"])
        for month in pay_out_months:
            from_date = month + "-01 00:00:00"
            to_date = self.utils.incr_month(from_date) + "-01 00:00:00"
            qparams = [self.session.get_session_value("_profile_seq"), from_date, to_date]
            post_earnings = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_EARNINGS",qparams)
            profile_earnings = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_EARNINGS",qparams)
            qparams = [self.session.get_session_value("_user_seq"), from_date, to_date]
            referal_earnings = self.connDB.execute_prepared_stmt("sotrueappuser","GET_REFERRAL_EARNINGS",qparams)

            total_month = 0
            ref_earning = 0
            sub_earning = 0
            if post_earnings and post_earnings[0].amount_earned:
                total_month = post_earnings[0].amount_earned
                sub_earning = post_earnings[0].amount_earned
            if profile_earnings and profile_earnings[0].amount_earned:
                total_month += profile_earnings[0].amount_earned
                sub_earning += profile_earnings[0].amount_earned
            if referal_earnings and referal_earnings[0].amount_earned:
                total_month += referal_earnings[0].amount_earned
                ref_earning = referal_earnings[0].amount_earned
            qparams = [self.utils.get_cur_timestamp(),self.session.get_session_value("_user_seq"),
                       month, total_month, ref_earning, sub_earning, "PENDING"]
            payout_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_PAYOUT_LOG",qparams)        

        if "gstn" in self.params:
            qparams = [self.params["gstn"], self.session.get_session_value("_user_seq")]
            self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_USER_GSTIN",qparams)

        response = self.create_response(dict(),"UE070")
        return(response)


    def get_post_like_users(self):
        qparams = [self.params["post_seq"]]
        user_list = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_LIKE_USER_DETAILS",qparams,limit=self.is_paginated())
        if not user_list:
            response = self.create_error("UE073")
            return(response)
        user_list = self.utils.convert_tuples_to_dicts(user_list)
        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger) 
        for rec in user_list:
            rec["like_time"] = self.utils.format_ts_display(rec["like_time"])
            rec["profile_picture"] = rec["profile_picture"] if not rec["profile_picture"] else generic_code.create_media_url(SotrueAppConfig.profile_path + rec["profile_picture"],AppConfigs.s3_profiles_folder, rec["s3_enabled"])
        user_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_LIKE_USER_DETAILS_COUNT",qparams)
        response = self.create_response(user_list,"UE011",addnl={"_total_rows":user_count[0].count})
        return(response)


    def search_post_like_users(self):
        qparams = [self.params["post_seq"], 
                   '%' + self.params["search_str"] + '%', '%' + self.params["search_str"] + '%']
        user_list = self.connDB.execute_prepared_stmt("sotrueappuser","SEARCH_POST_LIKE_USER",qparams,limit=self.is_paginated())
        if not user_list:
            response = self.create_error("UE074")
            return(response)
        user_list = self.utils.convert_tuples_to_dicts(user_list)
        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger) 
        for rec in user_list:
            rec["like_time"] = self.utils.format_ts_display(rec["like_time"])
            rec["profile_picture"] = rec["profile_picture"] if not rec["profile_picture"] else generic_code.create_media_url(SotrueAppConfig.profile_path + rec["profile_picture"],AppConfigs.s3_profiles_folder, rec["s3_enabled"])
        user_count = self.connDB.execute_prepared_stmt("sotrueappuser","SEARCH_POST_LIKE_USER_COUNT",qparams)
        response = self.create_response(user_list,"UE011",addnl={"_total_rows":user_count[0].count})
        return(response)
    

    def submit_user_feedback(self):
        qparams = [self.params["report_seq"]]
        report_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_FEEDBACK_DETAILS",qparams)
        if report_data:
            response = self.create_error("UE075")
            return(response)
        qparams = [self.params["report_seq"], self.params["profile_seq"], self.utils.get_cur_timestamp(),
                   self.params["rating"],self.params["comment"],"ACTIVE"]
        feedback_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_FEEDBACK_DETAILS",qparams)
        self.tracker.track_user_feedback()
        response = self.create_response(dict(feedback_seq=feedback_seq),"UE076")
        return(response)


    def delete_user_post_comment(self):
        qparams = ['INACTIVE',self.params["comment_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_USER_POST_COMMENT_STATUS",qparams)
        self.tracker.track_delete_comment_post(self.get_comment_user(self.params["comment_seq"]))
        response = self.create_response(dict(),"UE081")
        return(response)

    def update_post_comments(self):
        qparams = ["" if "comments" not in self.params else self.params["comments"],self.params["post_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_POST_COMMENTS",qparams)
        self.tracker.track_update_comment_post(self.get_post_user(self.params["post_seq"]))

        self.insert_tags(self.params["comments"] if "comments" in self.params else "","POST_CAPTION",
                            self.params["post_seq"])
        self.tracker.track_delete_comment_post(self.get_post_user(self.params["post_seq"]))
        response = self.create_response(dict(),"UE082")
        return(response)

    def save_fcm_key(self):
        qparams = [self.params['fcm_key'],self.session.get_session_value("_user_seq")]
        self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_FCM_KEY",qparams)
        response = self.create_response(dict(),"UE013")
        return(response)


    def get_seq_for_handle(self):
        qparams = [self.params['user_handle']]
        result_set = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_SEQ",qparams)
        if not result_set:
            response = self.create_error("UE012")
            return(response)
        response = self.utils.convert_tuples_to_dicts(result_set)        
        response = self.create_response(response,"UE011")
        return(response)


    def get_tagged_users(self):
        qparams = [self.params["post_seq"]]
        tagged_users = self.connDB.execute_prepared_stmt("sotrueappuser","GET_TAGGED_USERS",qparams)
        if not tagged_users:
            response = self.create_error("UE012")
            return(response)
        tagged_users = self.utils.convert_tuples_to_dicts(tagged_users)  
        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger) 
        for rec in tagged_users:
            rec["profile_picture"] = rec["profile_picture"] if not rec["profile_picture"] else generic_code.create_media_url(SotrueAppConfig.profile_path + rec["profile_picture"],AppConfigs.s3_profiles_folder,rec["s3_enabled"])
            rec["cover_image"] = rec["cover_image"] if not rec["cover_image"] else generic_code.create_media_url(SotrueAppConfig.profile_path + rec["cover_image"],AppConfigs.s3_profiles_folder,rec["s3_enabled"])
        response = self.create_response(tagged_users,"UE011")
        return(response)


    def get_posts_tagged(self):
        qparams = [self.params["profile_seq"]]
        posts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POSTS_TAGGED",qparams,limit=self.is_paginated())
        if not posts:
            response = self.create_error("UE065")
            return(response)
        post_data_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POSTS_TAGGED_COUNT",qparams)

        posts = sorted(posts, key=lambda d: d.tagged_on, reverse=True)

        post_seqs = []
        for rec in posts:
            if rec.post_seq not in post_seqs:
                post_seqs.append(rec.post_seq)
        
        subs = {"<POST_LIST>":self.utils.convert_to_delim_str(post_seqs,",")}
        post_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POSTS_DETAILS_TAGGED",qparams,subs=subs)                            

        post_data = self.utils.convert_tuples_to_dicts(post_data)
        post_seqs = []
        profile_seqs = []
        post_data_indexed = {}
        for row in post_data:
            post_seqs.append(row["post_seq"])
            profile_seqs.append(row["profile_seq"])
            post_data_indexed[row["post_seq"]] = row

        qparams = [self.session.get_session_value("_profile_seq")]
        subs = {"<POST_LIST>":self.utils.convert_to_delim_str(post_seqs,",")}                    
        post_views = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_VIEWS",qparams,subs=subs)
        post_bookmarks = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_BOOKMARKS",qparams,subs=subs)
        subscribed_posts = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_POST_SUBSCRIPION_LIST",qparams,subs=subs)
        post_comments = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_POST_COMMENTS",qparams,subs=subs)
        post_tags = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_TAG_LIST",None,subs=subs)
        post_reactions = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_REACTIONS_LIST",None,subs=subs)
        qparams.append("LIKE")
        post_likes = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_LIKES",qparams,subs=subs)

        post_likes_indexed = {}
        for rec in post_likes:
            post_likes_indexed[rec.post_seq] = rec.like_seq
        post_views_indexed = {}
        for rec in post_views:
            post_views_indexed[rec.post_seq] = rec.view_seq
        post_comments_indexed = {}
        for rec in post_comments:
            post_comments_indexed[rec.content_seq] = rec.comment_seq
        post_bookmark_indexed = {}
        for rec in post_bookmarks:
            post_bookmark_indexed[rec.post_seq] = rec.bookmark_seq
        subscribed_posts_indexed = {}
        for rec in subscribed_posts:
            subscribed_posts_indexed[rec.subscribed_post_seq] = rec.subscription_seq
        post_tags_indexed = {}
        for rec in post_tags:
            post_tags_indexed[rec.post_seq] = rec.post_seq
        post_reactions_indexed = {}
        for rec in post_reactions:
            if rec.post_seq not in post_reactions_indexed:
                post_reactions_indexed[rec.post_seq] = []
            post_reactions_indexed[rec.post_seq].append(rec.reaction)

        subs = {"<PROFILE_LIST>":self.utils.convert_to_delim_str(profile_seqs,",")}
        profile_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_FOR_USERS",None,subs=subs)
        profile_data_indexed = dict()
        for row in profile_data:
            profile_data_indexed[row.profile_seq] = row
            
        today = self.utils.get_today_date_str()
        qparams = [self.session.get_session_value("_profile_seq"),'ACTIVE',today,today]
        subscriptions = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_SUBSCRIPTION_LIST",qparams,subs=subs)
        subscriptions_indexed = dict()
        for sub in subscriptions:
            subscriptions_indexed[sub.subscribed_to_seq] = sub.subscription_seq

        subs = {"<POST_LIST>":self.utils.convert_to_delim_str(post_seqs,",")}       
        query_params = ["LIKE"]
        like_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_LIKE_COUNTS",query_params,subs=subs)
        view_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_VIEWS_COUNTS",None,subs=subs)
        qparams = ["POST"]
        comment_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_COMMENT_COUNTS",qparams,subs=subs)            
            
        like_counts_indexed = dict()
        view_counts_indexed = dict()
        comment_counts_indexed = dict()
        for like in like_counts:
            like_counts_indexed[like.post_seq] = like.count
        for view in view_counts:
            view_counts_indexed[view.post_seq] = view.count
        for comment in comment_counts:
            comment_counts_indexed[comment.content_seq] = comment.count

        subs = {"<PROFILE_LIST>":self.utils.convert_to_delim_str(profile_seqs,",")}
        qparams = [self.utils.get_ts_back_by(self.utils.get_cur_timestamp(),24)]
        story_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_STORY_COUNT",qparams,subs=subs)
        story_counts_indexed = dict()
        for story in story_counts:
            story_counts_indexed[story.profile_seq] = story.count

        qparams = [self.session.get_session_value("_profile_seq"),'ACTIVE','RESTRICTED']
        restricted_list = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_RESTRICTED_PROFILE_LIST",qparams,subs=subs)
        restricted_list_indexed = dict()
        for row in restricted_list:
            restricted_list_indexed[row.restricted_by] = row.restrict_seq

        gstn_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_GSTIN_LIST",None,subs=subs)
        gstn_data_indexed = {}
        for rec in gstn_data:
            if rec.gstin:
                gstn_data_indexed[rec.profile_seq] = rec.gstin

        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
        post_data_result = []
        for rec in posts: 
            row = None
            if rec.post_seq in post_data_indexed:
                row = post_data_indexed[rec.post_seq]
            else:
                continue

            row["likes"] = 0 if row["post_seq"] not in like_counts_indexed else like_counts_indexed[row["post_seq"]]
            row["views"] = 0 if row["post_seq"] not in view_counts_indexed else view_counts_indexed[row["post_seq"]]
            row["comments"] = 0 if row["post_seq"] not in comment_counts_indexed else comment_counts_indexed[row["post_seq"]]                
            row["user_handle"] = profile_data_indexed[row["profile_seq"]].user_handle                
            row["profile_picture"] = profile_data_indexed[row["profile_seq"]].profile_picture if not profile_data_indexed[row["profile_seq"]].profile_picture else generic_code.create_media_url(SotrueAppConfig.profile_path + profile_data_indexed[row["profile_seq"]].profile_picture,AppConfigs.s3_profiles_folder,profile_data_indexed[row["profile_seq"]].s3_enabled)
            row["display_name"] = profile_data_indexed[row["profile_seq"]].display_name
            row["enable_comment"] = profile_data_indexed[row["profile_seq"]].enable_comment
            row["enable_watermark"] = profile_data_indexed[row["profile_seq"]].enable_watermark
            row["is_liked"] = 'YES' if row["post_seq"] in post_likes_indexed else 'NO'
            row["is_viewed"] = 'YES' if row["post_seq"] in post_views_indexed else 'NO'
            row["is_bookmarked"] = 'YES' if row["post_seq"] in post_bookmark_indexed else 'NO'
            row["is_commented"] = 'YES' if row["post_seq"] in  post_comments_indexed else 'NO'
            row["posted_on"] = self.utils.get_formatted_time_past(row["posted_on"])
            row["story_count"] = 0 if row["profile_seq"] not in story_counts_indexed else story_counts_indexed[row["profile_seq"]]
            row["is_subscribed"] = 'YES' if row["post_seq"] in subscribed_posts_indexed else 'NO'
            if row["profile_seq"] not in gstn_data_indexed:                    
                row["viewer_fee_display"] = self.utils.format_currency(int(row["viewer_fee"]/100))
            else:                    
                row["viewer_fee_display"] = self.utils.format_currency(round((row["viewer_fee"] + (row["viewer_fee"]*0.18))/100,2))
            row["viewer_fee"] = int(row["viewer_fee"]/100)
            row["is_restricted"] = "YES" if row["profile_seq"] in restricted_list_indexed else "NO"
            row["is_tagged"] = "YES" if row["post_seq"] in post_tags_indexed else "NO"
            row["is_verified"] = profile_data_indexed[row["profile_seq"]].is_verified
            row["profile_type"] =profile_data_indexed[row["profile_seq"]].type
            if row["profile_type"] == "PAID" and row["profile_seq"] not in subscriptions_indexed:
                row["is_profile_subscribed"] = "NO"
            else:
                row["is_profile_subscribed"] = "YES"
            if profile_data_indexed[row["profile_seq"]].paid_account_fee:
                if profile_data_indexed[row["profile_seq"]].gstin:
                    row["profile_fee_display"]=self.utils.format_currency(round((profile_data_indexed[row["profile_seq"]].paid_account_fee + (profile_data_indexed[row["profile_seq"]].paid_account_fee*0.18))/100,2))
                else:
                    row["profile_fee_display"]=self.utils.format_currency(int(profile_data_indexed[row["profile_seq"]].paid_account_fee/100)) 
            else:
                row["profile_fee_display"]=0
            row["profile_fee"]=int(profile_data_indexed[row["profile_seq"]].paid_account_fee)/100 if profile_data_indexed[row["profile_seq"]].paid_account_fee else 0
            row["profile_fee_display"]=self.utils.format_currency(int(profile_data_indexed[row["profile_seq"]].paid_account_fee)/100) if profile_data_indexed[row["profile_seq"]].paid_account_fee else 0
            if (row["profile_type"] == "PAID" and row["is_profile_subscribed"] == "NO") or (row["post_type"] == "PAID" and row["is_subscribed"] != 'YES'):
                row["media_file"] = ""
                row["media_cover"]=""
                row["is_displayed"] = "NO"
                row["fuzzy_image"] = row["fuzzy_image"] if not row["fuzzy_image"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(row["profile_seq"]) + "/" + row["fuzzy_image"],AppConfigs.s3_posts_folder, row["s3_enabled"])
            else:
                row["media_file"] = generic_code.create_media_url(SotrueAppConfig.media_path + str(row["profile_seq"]) + "/" + row["media_file"],AppConfigs.s3_posts_folder, row["s3_enabled"])
                row["media_cover"] = row["media_cover"] if not row["media_cover"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(row["profile_seq"]) + "/" + row["media_cover"],AppConfigs.s3_posts_folder, row["s3_enabled"])
                row["is_displayed"] = "YES"
                row["fuzzy_image"] = ""
            if row["post_comments"]:
                s = [row["post_comments"]]
                row["post_caption_tags"] = self.validate_user_handles(s)
                row["post_comments"] = s[0]
            else:
                row["post_caption_tags"] = []
            row["reactions"] = post_reactions_indexed[row["post_seq"]] if row["post_seq"] in post_reactions_indexed else []
            post_data_result.append(row)

        response = self.create_response(post_data_result,"UE011",addnl={"_total_rows":post_data_count[0].count})
        return(response)


    def get_user_to_tag(self,profile_seq=None):        
        qparams = [self.params["profile_seq"] if not profile_seq else profile_seq]        
        following_user = self.connDB.execute_prepared_stmt("sotrueappuser","GET_FOLLOWING_PROFILES",qparams)        
        qparams = [self.params["profile_seq"] if not profile_seq else profile_seq,
                    self.utils.get_today_date_str(),self.utils.get_today_date_str()]
        subscribing_user = self.connDB.execute_prepared_stmt("sotrueappuser","GET_SUBSCRIBING_PROFILES",qparams)
        
        all_following_user = []        
        for rec in following_user:
            if rec.follower_profile_seq not in all_following_user:
                all_following_user.append(rec.follower_profile_seq)
        for rec in subscribing_user:
            if rec.subscribed_by_seq not in all_following_user:
                all_following_user.append(rec.subscribed_by_seq)

        if not all_following_user:
            response = self.create_error("UE012")
            return(response)

        subs = {"<PROFILES_LIST>":self.utils.convert_to_delim_str(all_following_user,",")}
        qparams = [self.params["profile_seq"] if not profile_seq else profile_seq]
        followed_by_user = self.connDB.execute_prepared_stmt("sotrueappuser","GET_FOLLOWED_BY_PROFILES",qparams,subs=subs)
        qparams = [self.params["profile_seq"] if not profile_seq else profile_seq,
                   self.utils.get_today_date_str(),self.utils.get_today_date_str()]
        subscribed_by_user = self.connDB.execute_prepared_stmt("sotrueappuser","GET_SUBSCRIBED_BY_PROFILES",qparams,subs=subs)
        
        result_seqs = []
        for rec in followed_by_user:
            if rec.following_profile_seq not in result_seqs:
                result_seqs.append(rec.following_profile_seq)
        for rec in subscribed_by_user:
            if rec.subscribed_to_seq not in result_seqs:
                result_seqs.append(rec.subscribed_to_seq)

        if not result_seqs:
            response = self.create_error("UE012")
            return(response)

        subs = {"<PROFILES_LIST>":self.utils.convert_to_delim_str(result_seqs,",")}
        qparams = None
        if "search_str" in self.params:
            qparams = ["%" + self.params["search_str"] + "%", "%" + self.params["search_str"] + "%"]
        else:
            qparams = ["%","%"]
        user_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS_LIST",qparams,subs=subs,limit=self.is_paginated())

        if not user_data:
            response = self.create_error("UE012")
            return(response)
        user_data_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS_LIST_COUNT",qparams,subs=subs)

        user_data = self.utils.convert_tuples_to_dicts(user_data)
        if profile_seq:
            return user_data

        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
        for rec in user_data:                        
            rec["profile_picture"] = rec["profile_picture"] if not rec["profile_picture"] else  generic_code.create_media_url(SotrueAppConfig.profile_path + rec["profile_picture"],AppConfigs.s3_profiles_folder,rec["s3_enabled"])            

        response = self.create_response(user_data,"UE011",addnl={"_total_rows":user_data_count[0].count})
        return(response)


    def validate_tagging(self):
        qparams = [self.session.get_session_value("_profile_seq")]
        user_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)

        qparams = [self.params["tag_profile_seq"]]
        tag_user_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)

        msg = None
        if user_data[0].type == "NOTPAID" and tag_user_data[0].type == "NOTPAID":
            qparams = [self.session.get_session_value("_profile_seq"),self.params["tag_profile_seq"]]
            check1 = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_FOLLOWING",qparams)
            qparams = [self.params["tag_profile_seq"],self.session.get_session_value("_profile_seq")]
            check2 = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_FOLLOWING",qparams)
            if not check1 or not check2:
                msg = "UE084"
        elif user_data[0].type == "PAID" and tag_user_data[0].type == "NOTPAID":
            qparams = [self.params["tag_profile_seq"],self.session.get_session_value("_profile_seq"),
                       self.utils.get_today_date_str(),self.utils.get_today_date_str()]
            check1 = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_SUBSCRIBING",qparams)
            if not check1:
                msg = "UE085"
        elif user_data[0].type == "NOTPAID" and tag_user_data[0].type == "PAID":
            qparams = [self.session.get_session_value("_profile_seq"),self.params["tag_profile_seq"],
                       self.utils.get_today_date_str(),self.utils.get_today_date_str()]
            check1 = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_SUBSCRIBING",qparams)
            if not check1:
                msg = "UE086"
        elif user_data[0].type == "PAID" and tag_user_data[0].type == "PAID":
            qparams = [self.session.get_session_value("_profile_seq"),self.params["tag_profile_seq"],
                       self.utils.get_today_date_str(),self.utils.get_today_date_str()]
            check1 = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_SUBSCRIBING",qparams)
            qparams = [self.params["tag_profile_seq"],self.session.get_session_value("_profile_seq"),
                       self.utils.get_today_date_str(),self.utils.get_today_date_str()]
            check2 = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_SUBSCRIBING",qparams)
            if not check1 or not check2:
                msg = "UE087"

        if not msg:
            msg = "UE088"
            response = self.create_response(dict(),msg)        
        else:
            response = self.create_error(msg)            
        return(response)


    def get_notifications_tag(self):
        qparams = [self.session.get_session_value("_profile_seq")]
        notifications = self.connDB.execute_prepared_stmt("sotrueappuser","GET_TAG_NOTIFICATIONS",qparams,limit=self.is_paginated()) 

        if not notifications:
            response = self.create_error("UE089")
            return(response)

        notification_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_TAG_NOTIFICATIONS_COUNT",qparams) 
        notifications = self.utils.convert_tuples_to_dicts(notifications)
        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
        for rec in notifications:                        
            rec["profile_picture"] = rec["profile_picture"] if not rec["profile_picture"] else  generic_code.create_media_url(SotrueAppConfig.profile_path + rec["profile_picture"],AppConfigs.s3_profiles_folder,rec["s3_enabled"])            
            rec["tagged_on"] = self.utils.get_formatted_time_past(rec["tagged_on"])
            if rec["type"] == "POST" or rec["type"] == "POST_CAPTION":
                rec["msg"] = rec["display_name"] + " has tagged you on a post. Check it out!"
            elif rec["type"] == "STORY_CAPTION":
                rec["msg"] = rec["display_name"] + " has tagged you in their story. Check it out!"            
            elif rec["type"] == "PROFILE_BIO":
                rec["msg"] = rec["display_name"] + " has tagged you in their bio. Check it out!"

        response = self.create_response(notifications,"UE011",addnl={"_total_rows":notification_count[0].count})
        return(response)


    def get_story_details(self):
        qparams = [self.params["story_seq"]]        
        story = self.connDB.execute_prepared_stmt("sotrueappuser","GET_STORY_DETAILS",qparams)

        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
        if story:
            story = self.utils.convert_tuples_to_dicts(story)
            row = story[0]
            row["profile_picture"] = row["profile_picture"] if not row["profile_picture"] else generic_code.create_media_url(SotrueAppConfig.profile_path + row["profile_picture"],AppConfigs.s3_profiles_folder, row["s3_profile"])
            row["media_file"] = generic_code.create_media_url(SotrueAppConfig.media_path + str(self.params["profile_seq"]) + "/" + row["media_file"],AppConfigs.s3_stories_folder, row["s3_story"])
            row["media_cover"] = row["media_cover"] if not row["media_cover"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(self.params["profile_seq"]) + "/" + row["media_cover"],AppConfigs.s3_stories_folder, row["s3_story"])
            if row["media_type"] == "VIDEO" and row["video_duration"] == 0:
                duration = self.utils.get_video_duration(SotrueAppConfig.media_path + str(self.params["profile_seq"]) + "/" + row["media_file"])
                row["video_duration"] = duration
                qparams [duration, row["story_seq"]]
                self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_VIDEO_DURATION_STORY",qparams)
            if row["story_comments"]:
                s=[row["story_comments"]]
                row["story_comment_tags"] = self.validate_user_handles(s)
                row["story_comments"] = s[0]
            else:
                row["story_comment_tags"] = []

            response = self.create_response(story,"UE011")
            return(response)
        
        response = self.create_error("UE012")
        return(response)


    def validate_user_handles(self, input):
        sentence = input[0]
        sentence = sentence.replace("&amp;commat;","@")
        sentence = sentence.replace("&amp;NewLine;","\n")
        words = sentence.split()
        tags = []
        for word in words:
            if word.startswith("@") and word[1:] not in tags:
                tags.append(word[1:])
        if not tags:
            return []
        subs = {"<TAG_LIST>":self.utils.convert_to_delim_str(tags,",","'")}
        history = self.connDB.execute_prepared_stmt("sotrueappuser","GET_HANDLE_HISTORY",None,subs=subs)
        history_indexed = {}
        for rec in history:
            history_indexed[rec.old_handle] = rec
        new_tags = []
        for tag in tags:
            if tag in history_indexed:
                new_tags.append(history_indexed[tag].new_handle)
                sentence = sentence.replace("@"+tag,"@"+history_indexed[tag].new_handle)
            else:
                new_tags.append(tag)
        subs = {"<TAG_LIST>":self.utils.convert_to_delim_str(new_tags,",","'")}
        handles = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_FOR_HANDLE",None,subs=subs)

        #allowed = self.get_user_to_tag(self.session.get_session_value("_profile_seq"))        
        #allowed_indexed = []  
        #if type(allowed) is list:
        #    for rec in allowed:
        #        allowed_indexed.append(rec["user_handle"])
        response = []
        for handle in handles:
            #if handle.user_handle in allowed_indexed:
            response.append({"handle":"@" + handle.user_handle, "profile_seq":handle.profile_seq})
        input[0] = sentence
        return response


    def insert_tags(self,sentence,type,seq,allowed=None):
        qparams = [self.session.get_session_value("_profile_seq"),type,seq]
        self.connDB.execute_prepared_stmt("sotrueappuser","REMOVE_POST_TAG",qparams)  
        s = [sentence]
        tags = self.validate_user_handles(s)

        if tags:
            qparams = [self.session.get_session_value("_profile_seq")]
            self_profile = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)

            allowed_list = []
            if allowed:
                for rec in allowed:
                    allowed_list.append(rec["handle"])

            for tag in tags:
                qparams = [tag["handle"][1:]]
                if allowed and tag["handle"] not in allowed_list:
                    continue
                user_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_SEQ",qparams)
                qparams = [self.session.get_session_value("_profile_seq"),user_details[0].profile_seq,                       
                            self.utils.get_cur_timestamp(), seq, type]
                tag_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_POST_TAG",qparams)                

                if user_details[0].fcm_key:                    
                    label = "Post" if type=="POST_CAPTION" else ("Story" if type=="STORY_CAPTION" else "Bio")
                    payload = {"type":type,"post_seq":str(seq)}
                    fcm = AppFirebase()
                    fcm.send_fcm_notification(user_details[0].fcm_key,
                        label + " Tag Alert", "Hey! Hey! " + self_profile[0].display_name + " has tagged you in a " + label + "!",
                                                    payload)
            self.tracker.track_tag_post(self.get_post_user(seq))
    def update_notification_view(self):
        qparams = [self.session.get_session_value("_profile_seq"),self.params["view_tab"]]
        notification_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_NOTIFICATION_VIEW",qparams)
        if notification_data:
             qparams = [self.utils.get_cur_timestamp(),notification_data[0].notification_seq]
             self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_NOTIFICATION_VIEW",qparams)
        else:
            qparams = [self.utils.get_cur_timestamp(),
                       self.session.get_session_value("_profile_seq"),self.params["view_tab"]]
            self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_NOTIFICATION_VIEW",qparams)
        
        response = self.create_response(dict(),"UE013")
        return(response)


    def get_notification_status(self):
        qparams = [self.session.get_session_value("_profile_seq")]
        notification_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_NOTIFICATION_VIEW_ALL",qparams)
        notification_data_indexed = {}
        for rec in notification_data:
            notification_data_indexed[rec.view_type] = rec.last_viewed
        default_time= self.utils.get_ts_back_by(self.utils.get_cur_timestamp(),24*7)
        response = []

        qparams = [self.session.get_session_value("_profile_seq"),'ACTIVE',
                   default_time if "SUBSCRIBE" not in notification_data_indexed else notification_data_indexed["SUBSCRIBE"]]
        subs_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_SUBSCRIBE_NOTIFICATIONS",qparams)
        if(subs_details):
            response.append({"view_tab":"SUBSCRIBE","status":"YES"})
        else:
             response.append({"view_tab":"SUBSCRIBE","status":"NO"})

        qparams = [default_time if "LIKE" not in notification_data_indexed else notification_data_indexed["LIKE"],
                   self.session.get_session_value("_profile_seq")]
        notifications = self.connDB.execute_prepared_stmt("sotrueappuser","GET_LIKE_NOTIFICATIONS",qparams) 
        if(notifications):
            response.append({"view_tab":"LIKE","status":"YES"})
        else:
             response.append({"view_tab":"LIKE","status":"NO"})

        qparams = ['POST','ACTIVE',self.session.get_session_value("_profile_seq"),
                   default_time if "COMMENTS" not in notification_data_indexed else notification_data_indexed["COMMENTS"]]
        comment_list = self.connDB.execute_prepared_stmt("sotrueappuser","GET_COMMENT_NOTIFICATIONS",qparams)
        if(comment_list):
            response.append({"view_tab":"COMMENTS","status":"YES"})
        else:
             response.append({"view_tab":"COMMENTS","status":"NO"})

        qparams = [self.session.get_session_value("_profile_seq"),
                   default_time if "FOLLOW" not in notification_data_indexed else notification_data_indexed["FOLLOW"]]
        follow_details = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_FOLLOW_NOTIFICATIONS",qparams)
        if(follow_details):
            response.append({"view_tab":"FOLLOW","status":"YES"})
        else:
             response.append({"view_tab":"FOLLOW","status":"NO"})

        qparams = [self.session.get_session_value("_profile_seq"),
                   default_time if "TAG" not in notification_data_indexed else notification_data_indexed["TAG"]]
        notifications = self.connDB.execute_prepared_stmt("sotrueappuser","GET_TAG_NOTIFICATIONS_COUNT_DATE",qparams)
        if notifications[0].count:
            response.append({"view_tab":"TAG","status":"YES"})
        else:
             response.append({"view_tab":"TAG","status":"NO"})

        response = self.create_response(response,"UE011")
        return(response)


    def get_financial_report(self):
        from_date = None        
        today = self.utils.get_today_date_str()
        to_date = self.utils.retard_date_by(today,1) + " 23:59:59"

        if self.params["date_range"] == "CURRENT_DAY_USER":
            from_date = today + " 00:00:00"
            to_date = today + " 23:59:59"
        elif self.params["date_range"] == "PREV_DAY_USER":            
            from_date = self.utils.retard_date_by(today,1) + " 00:00:00"            
        elif self.params["date_range"] == "PAST_7_DAYS_USER":
            from_date = self.utils.retard_date_by(today,7) + " 00:00:00"            
        elif self.params["date_range"] == "PAST_15_DAY_USER":
            from_date = self.utils.retard_date_by(today,15) + " 00:00:00"            
        elif self.params["date_range"] == "PAST_30_DAY_USER":
            from_date = self.utils.retard_date_by(today,30) + " 00:00:00"            
        elif self.params["date_range"] == "PAST_60_DAY_USER":
            from_date = self.utils.retard_date_by(today,60) + " 00:00:00"
        elif self.params["date_range"] == "PAST_90_DAY_USER":
            from_date = self.utils.retard_date_by(today,90) + " 00:00:00"
        elif self.params["date_range"] == "PAST_180_DAY_USR":
            from_date = self.utils.retard_date_by(today,180) + " 00:00:00"
        elif self.params["date_range"] == "LIFETIME_USER":
            from_date = "2021-01-01 00:00:00"
            to_date = today + " 23:59:59"

        qparams = [from_date,to_date,self.session.get_session_value("_profile_seq")]
        post_payments = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PAYMENTS_POST",qparams)
        profile_payments = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PAYMENTS_PROFILE",qparams)
        referral_payments = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PAYMENTS_REFERRAL",qparams)

        post_payments = self.utils.convert_tuples_to_dicts(post_payments)
        profile_payments = self.utils.convert_tuples_to_dicts(profile_payments)
        referral_payments = self.utils.convert_tuples_to_dicts(referral_payments)

        payment_data = []
        for rec in post_payments:
            rec["type"] = "Post"
            payment_data.append(rec)
        for rec in profile_payments:
            rec["type"] = "Profile"
            payment_data.append(rec)
        for rec in referral_payments:
            rec["type"] = "Referral"
            payment_data.append(rec)
            
        payment_data = sorted(payment_data, key=lambda d: d["paid_on"], reverse=True)

        excel_data = []
        for rec in payment_data:
            row = [rec["paid_on"],rec["type"],
                   self.utils.format_currency(int(rec["paid_amount"]/100)),
                   rec["display_name"],rec["user_handle"],
                   self.utils.format_currency(int(rec["gst"]/100)),
                   self.utils.format_currency(int(rec["comission"]/100)) if "comission" in rec else "-",
                   self.utils.format_currency(int(rec["gst_comission"]/100)) if "gst_comission" in rec else "-",
                   self.utils.format_currency(int(rec["tds"]/100)),
                   self.utils.format_currency(int(rec["tcs"]/100)) if "tcs" in rec else "-",
                   self.utils.format_currency(int(rec["inward_charges"]/100)) if "inward_charges" in rec else "-",
                   self.utils.format_currency(int(rec["outward_charges"]/100)),
                   self.utils.format_currency(int(rec["payable"]/100))]
            excel_data.append(row)

        extn = ".xlsx"
        file_name = "Payments_Report_" + self.utils.get_time_for_file()+"_"+str(self.session.get_session_value("_user_seq"))+extn

        heading = "Payments Report"
        summary = ["Created On: " + self.utils.get_current_ts_display(),
                   "From Date: " + self.utils.format_date_display(from_date)  if self.params["date_range"] != "LIFETIME_USER" else "From Date: Minimum",
                   "To Date: " + self.utils.format_date_display(to_date) if self.params["date_range"] != "LIFETIME_USER" else "To Date: Maximum"]

        writer = AppExcel()
        col_headings = ["Paid On","Paid For","Base Amount","Paid By","Paid By Handle",
                        "GST","Commission","GST Commission","TDS","TCS","Inward Charges","Outward Charges","Payable"]
        writer.write_to_excel(os.path.join(SotrueAppConfig.report_temp_path,file_name),excel_data,col_headings,heading,summary)

        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
        response = [{"file_path":generic_code.create_media_url(SotrueAppConfig.report_temp_path + file_name,None,"NO")}]        
        response = self.create_response(response,"UE011")
        return(response)


    def get_popular_categories(self):
        categories = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POPULAR_CATEGORIES",None)
        if not categories:
            response = self.create_error("UE012")
            return(response)
        codes = []
        for rec in categories:
            codes.append(rec.category)
        subs = {"<CONFIG_KEYS>":self.utils.convert_to_delim_str(codes,",","'")}
        values = self.connDB.execute_prepared_stmt("sotrueappuser","GET_VALUES_FOR_CODES",None,subs=subs)
        response = self.utils.convert_tuples_to_dicts(values)
        response = self.create_response(response,"UE011")
        return(response)


    def get_post_user(self, post_seq):
        qparams = [post_seq]
        user_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_USER",qparams)
        if user_data:
            return(user_data[0].email_id)
        else:
            return("Unknown")

    def get_story_user(self, post_seq):
        qparams = [post_seq]
        user_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_FOR_STORY",qparams)
        if user_data:
            return(user_data[0].email_id)
        else:
            return("Unknown")

    def get_profile_user(self, profile_seq):
        qparams = [profile_seq]
        user_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_USER",qparams)
        if user_data:
            return(user_data[0].email_id)
        else:
            return("Unknown")

    def get_comment_user(self, comment_seq):
        qparams = [comment_seq]
        user_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_USER_COMMENT",qparams)
        if user_data:
            return(user_data[0].email_id)
        else:
            return("Unknown")

    def get_user(self, user_seq):
        qparams = [user_seq]
        user_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_EMAIL_FOR_USER",qparams)
        if user_data:
            return(user_data[0].email_id)
        else:
            return("Unknown")


    def deactivate_user_data(self, status, profile_seq):
        qparams = [status,profile_seq]
        self.connDB.execute_prepared_stmt("sotrueappuser","SET_USER_STORIES_STATUS",qparams)     
        self.connDB.execute_prepared_stmt("sotrueappuser","SET_USER_POST_STATUS",qparams)
        self.connDB.execute_prepared_stmt("sotrueappuser","SET_POST_COMMENT_LIKES_STATUS",qparams)
        self.connDB.execute_prepared_stmt("sotrueappuser","SET_POST_COMMENTS_STATUS",qparams)
        self.connDB.execute_prepared_stmt("sotrueappuser","SET_POST_LIKE_STATUS",qparams)

        qparams = [status,profile_seq,profile_seq]
        self.connDB.execute_prepared_stmt("sotrueappuser","SET_PROFILE_FOLLOWER_STATUS",qparams)
        self.connDB.execute_prepared_stmt("sotrueappuser","SET_PROFILE_SUBSCRIPTION_STATUS",qparams)
        self.connDB.execute_prepared_stmt("sotrueappuser","SET_POST_TAGS_STATUS",qparams)
        self.connDB.execute_prepared_stmt("sotrueappuser","SET_POST_SUBSCRIPTION_STATUS",qparams)


    def send_email(self):
        mailer = AppEmail()
        mailer.send_email("<EMAIL>",None,"Contact request at sotrue.co.in",
            "We’ve received a contact request from our website with the following details:\n\n" +\
                "First Name: " + self.params["first_name"] + "\n" +\
                "Last Name: " + (self.params["last_name"] if "last_name" in self.params else "-") + "\n" +\
                "Email: " + self.params["email"] + "\n" +\
                "Mobile: " + (self.params["mobile"] if "mobile" in self.params else "-") + "\n" +\
                "Comments: " + (self.params["comments"] if "comments" in self.params else "-") + "\n\n" +
                "Thank you for reaching out. We will review your request and get back to you shortly.\n\n" +
                "Best,\nTeam SoTrue");
        response = self.create_response(dict(),"UE094")
        return(response)    


    def submit_post_view(self):
        qparams = [self.params['post_seq'],self.session.get_session_value("_profile_seq")]
        view_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_VIEW_PROFILE",qparams)
        view_seq = None
        if not view_data:            
            qparams = [self.params['post_seq'],self.session.get_session_value("_profile_seq"), self.utils.get_cur_timestamp()]
            view_seq = self.connDB.execute_prepared_stmt("sotrueappuser","SAVE_POST_VIEW",qparams)
            self.tracker.track_view_post(self.get_post_user(self.params['post_seq']))
        else:
            view_seq = view_data[0].view_seq

        qparams = [self.params['post_seq']]
        like_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_COUNT_POST_LIKE",qparams)        
        view_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_COUNT_POST_VIEW",qparams)  
                
        response = self.create_response(dict(view_seq=view_seq,like_count=like_count[0].count,view_count=view_count[0].count),"UE013")
        return(response)


    def update_share_count(self):
        qparams = [self.params["post_seq"]]
        share_count = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_SHARES",qparams)  
        if share_count:
            qparams = [share_count[0].count+1,self.params["post_seq"]]
            self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_POST_SHARES",qparams)  
        else:
            qparams = [1,self.params["post_seq"]]
            self.connDB.execute_prepared_stmt("sotrueappuser","ADD_POST_SHARES",qparams) 
        
        self.tracker.track_share(self.params["post_seq"])

        response = self.create_response(dict(),"UE013")
        return(response)


    def update_verify_notify(self):
        qparams = ["YES",self.params["profile_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_VERIFY_NOTIFIED",qparams)  
        response = self.create_response(dict(),"UE013")
        return(response)


    def get_genre_topic_masters(self):
        # Get genre masters
        genre_masters = self.connDB.execute_prepared_stmt("sotrueappuser","GET_GENRE_MASTERS",None)
        genre_masters_data = self.utils.convert_tuples_to_dicts(genre_masters) if genre_masters else []

        # Get topic masters
        topic_masters = self.connDB.execute_prepared_stmt("sotrueappuser","GET_TOPIC_MASTERS",None)
        topic_masters_data = self.utils.convert_tuples_to_dicts(topic_masters) if topic_masters else []

        response_data = {
            "genre_masters": genre_masters_data,
            "topic_masters": topic_masters_data
        }
        response = self.create_response(response_data, "UE011")
        return response

    def get_interest_list(self):
        qparams = ['ACTIVE']
        interests = self.connDB.execute_prepared_stmt("sotrueappuser","GET_ALL_INTERESTS",qparams)  
        if not interests:
            response = self.create_error("UE012")
            return(response)   
        interests = self.utils.convert_tuples_to_dicts(interests)

        selections = []
        count = 0
        if len(interests) > 18:
            while count < 6:
                sel = random.randint(0, len(interests)-1)
                if sel not in selections:
                    selections.append(sel);
                    count += 1

        random_interests = []
        for sel in selections:
            random_interests.append(interests[sel]["interest_name"])

        response = self.create_response(interests,"UE011",addnl={"random":random_interests})
        return(response)


    def get_interest_categories(self):
        qparams = ['ACTIVE']
        categories = self.connDB.execute_prepared_stmt("sotrueappuser","GET_ALL_INTEREST_CATEGORIES",qparams)  
        if not categories:
            response = self.create_error("UE012")
            return(response)   
        categories = self.utils.convert_tuples_to_dicts(categories)

        selections = []
        count = 0
        while count < int(len(categories)/4):
            sel = random.randint(0, len(categories)-1)
            if sel not in selections:
                selections.append(sel)
                count += 1

        random_cats = []
        for sel in selections:
            random_cats.append(categories[sel]["category"])

        response = self.create_response(categories,"UE011",addnl={"random":random_cats})
        return(response)


    def get_user_interests(self):
        qparams = [self.session.get_session_value("_user_seq"),'ACTIVE']
        interests = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_INTERESTS",qparams)  
        if not interests:
            response = self.create_error("UE012")
            return(response)   
        interests = self.utils.convert_tuples_to_dicts(interests)
        response = self.create_response(interests,"UE011")
        return(response)


    def update_user_interest(self):
        qparams = [self.params["user_id"]]
        # self.connDB.execute_prepared_stmt("sotrueappuser","DELETE_USER_INTERESTS",qparams)
        
        # user_interests = self.utils.parse_json(self.params["interests"])
        # for interest in user_interests:
        #     qparams = [self.params["user_id"],interest,'ACTIVE']
        #     interest_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_USER_INTEREST",qparams)
        
        qparams = ["YES",self.params["user_id"]]
        self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_USER_PERSONALIZED",qparams)

        response = self.create_response(dict(),"UE015")
        return(response)


    def update_user_ui_colour(self):
        qparams = [self.params["ui_colour"],self.session.get_session_value("_user_seq")]
        self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_USER_UI_COLOUR",qparams)
        qparams = ["YES",self.session.get_session_value("_user_seq")]
        self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_USER_PERSONALIZED",qparams) 
        response = self.create_response(dict(),"UE015")
        return(response)


    def get_location_list(self):
        locations = ["Ranga Shankara, Bengaluru","Medai - The Stage Bengaluru","Vyoma Artspace, Bangaluru"]
        response = self.create_response(locations,"UE011")
        return(response)


    def get_playlist_shows(self):
        qparams = [self.utils.get_cur_timestamp(), self.utils.get_cur_timestamp(),
                   self.params["req_user_seq"] if "req_user_seq" in self.params else self.session.get_session_value("_user_seq")]
        show_list = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_SHOWS",qparams)
        if not show_list:
            response = self.create_error("UE012")
            return(response)
        show_list = self.utils.convert_tuples_to_dicts(show_list)
        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)

        for show in show_list:
            show["logo_file"] = show["logo_file"] if not show["logo_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + show["logo_file"],AppConfigs.s3_posts_folder,show["s3_enabled"])
            show["thumb_file"] = show["thumb_file"] if not show["thumb_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + show["thumb_file"],AppConfigs.s3_posts_folder,show["s3_enabled"])
            show["preview_file"] = show["preview_file"] if not show["preview_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + show["preview_file"],AppConfigs.s3_posts_folder,show["s3_enabled"])

        qparams = [self.params["req_user_seq"] if "req_user_seq" in self.params else self.session.get_session_value("_user_seq")]
        master_image = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PLAYLIST_MASTER",qparams)  
        playlist_master_image = None
        if master_image and master_image[0].playlist_master_image:
            playlist_master_image = generic_code.create_media_url(SotrueAppConfig.media_path + master_image[0].playlist_master_image,AppConfigs.s3_posts_folder,"YES")

        response = self.create_response(show_list,"UE011",addnl={"master_image":playlist_master_image,
                                                                 "user_handle":master_image[0].user_handle,
                                                                 "display_name":master_image[0].display_name,
                                                                 "view_count":0})
        return(response)


    def get_playlist_episodes(self):
        if self.params["type"] == "SHOW":
            qparams = [self.params["sequence"]]
            seasons = self.connDB.execute_prepared_stmt("sotrueappuser","GET_SEASONS",qparams)  
            if seasons:
                seasons = self.utils.convert_tuples_to_dicts(seasons)
                generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
                for rec in seasons:
                    rec["cover_image"] = rec["cover_image"] if not rec["cover_image"] else generic_code.create_media_url(SotrueAppConfig.media_path + rec["cover_image"],AppConfigs.s3_posts_folder,"YES")
                    rec["banner_file"] = rec["banner_file"] if not rec["banner_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + rec["banner_file"],AppConfigs.s3_posts_folder,"YES")

                qparams = [self.params["sequence"]]
                show = self.connDB.execute_prepared_stmt("sotrueappuser","GET_SHOW_DETAILS",qparams)  
                show = self.utils.convert_tuples_to_dicts(show)
                show[0]["thumb_file"] = show[0]["thumb_file"] if not show[0]["thumb_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + show[0]["thumb_file"],AppConfigs.s3_posts_folder,"YES")        
                show[0]["banner_file"] = show[0]["banner_file"] if not show[0]["banner_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + show[0]["banner_file"],AppConfigs.s3_posts_folder,"YES")

                response = self.create_response(seasons,"UE011",addnl={"type":"SEASON","show":show[0]})                
                return(response)
            else:
                generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
                qparams = [self.utils.get_cur_timestamp(),self.utils.get_cur_timestamp(),self.params["sequence"]]
                episodes = self.connDB.execute_prepared_stmt("sotrueappuser","GET_EPISODES",qparams)  
                if not episodes:
                    qparams = [self.params["sequence"]]
                    show = self.connDB.execute_prepared_stmt("sotrueappuser","GET_SHOW_DETAILS",qparams)  
                    show = self.utils.convert_tuples_to_dicts(show)
                    show[0]["thumb_file"] = show[0]["thumb_file"] if not show[0]["thumb_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + show[0]["thumb_file"],AppConfigs.s3_posts_folder,"YES")        
                    show[0]["banner_file"] = show[0]["banner_file"] if not show[0]["banner_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + show[0]["banner_file"],AppConfigs.s3_posts_folder,"YES")

                    response = self.create_response([],"UE012",addnl={"type":"EPISODE","show":show[0]})
                    return(response)
                    #response = self.create_error("UE012")
                    #return(response)

                episodes = self.utils.convert_tuples_to_dicts(episodes)                

                for rec in episodes:                
                    rec["thumb_file"] = rec["thumb_file"] if not rec["thumb_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + rec["thumb_file"],AppConfigs.s3_posts_folder,rec["s3_enabled"])
                    rec["preview_file"] = rec["preview_file"] if not rec["preview_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + rec["preview_file"],AppConfigs.s3_posts_folder,rec["s3_enabled"])                
                    rec["banner_file"] = rec["banner_file"] if not rec["banner_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + rec["banner_file"],AppConfigs.s3_posts_folder,rec["s3_enabled"])

                qparams = [self.params["sequence"]]
                show = self.connDB.execute_prepared_stmt("sotrueappuser","GET_SHOW_DETAILS",qparams)  
                show = self.utils.convert_tuples_to_dicts(show)
                show[0]["thumb_file"] = show[0]["thumb_file"] if not show[0]["thumb_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + show[0]["thumb_file"],AppConfigs.s3_posts_folder,"YES")        
                show[0]["banner_file"] = show[0]["banner_file"] if not show[0]["banner_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + show[0]["banner_file"],AppConfigs.s3_posts_folder,"YES")        

                response = self.create_response(episodes,"UE011",addnl={"type":"EPISODE","show":show[0]})
                return(response)

        elif self.params["type"] == "SEASON":
            qparams = [self.utils.get_cur_timestamp(),self.utils.get_cur_timestamp(),self.params["sequence"]]
            episodes = self.connDB.execute_prepared_stmt("sotrueappuser","GET_EPISODES_SEASON",qparams)  
            if not episodes:
                response = self.create_error("UE012")
                return(response)

            episodes = self.utils.convert_tuples_to_dicts(episodes)
            generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)

            for rec in episodes:                
                rec["thumb_file"] = rec["thumb_file"] if not rec["thumb_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + rec["thumb_file"],AppConfigs.s3_posts_folder,rec["s3_enabled"])
                rec["preview_file"] = rec["preview_file"] if not rec["preview_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + rec["preview_file"],AppConfigs.s3_posts_folder,rec["s3_enabled"])
                rec["banner_file"] = rec["banner_file"] if not rec["banner_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + rec["banner_file"],AppConfigs.s3_posts_folder,rec["s3_enabled"])

            qparams = [self.params["sequence"]]
            season = self.connDB.execute_prepared_stmt("sotrueappuser","GET_SEASON_DETAILS",qparams)  
            season = self.utils.convert_tuples_to_dicts(season)
            season[0]["cover_image"] = season[0]["cover_image"] if not season[0]["cover_image"] else generic_code.create_media_url(SotrueAppConfig.media_path + season[0]["cover_image"],AppConfigs.s3_posts_folder,"YES")        

            response = self.create_response(episodes,"UE011",addnl={"type":"EPISODE","season":season[0]})
            return(response)

        response = self.create_error("UE012")
        return(response)


    def get_playlist_clips(self):
        # Log request parameters
        self.logger.log_message("=== GET_PLAYLIST_CLIPS REQUEST ===")
        self.logger.log_message(f"Request params: {self.params}")

        qparams = [self.params["season_seq"]]
        clips = self.connDB.execute_prepared_stmt("sotrueappuser","GET_SEASON_CLIPS",qparams)
        if not clips:
            response = self.create_error("UE012")
            self.logger.log_message("=== GET_PLAYLIST_CLIPS RESPONSE (ERROR) ===")
            self.logger.log_message(f"Response: {response}")
            return(response)

        clips = self.utils.convert_tuples_to_dicts(clips)

        post_seqs = []            
        for row in clips:
            post_seqs.append(row["post_seq"])
            
        subs = {"<POST_LIST>":self.utils.convert_to_delim_str(post_seqs,",")}                        
        qparams = [self.session.get_session_value("_profile_seq")]
        post_bookmarks = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_BOOKMARKS",qparams,subs=subs)        
        post_comments = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_POST_COMMENTS",qparams,subs=subs)                    
        qparams.append("LIKE")
        post_likes = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_LIKES",qparams,subs=subs)

        post_likes_indexed = {}
        for rec in post_likes:
            post_likes_indexed[rec.post_seq] = rec.like_seq            
        post_comments_indexed = {}
        for rec in post_comments:
            post_comments_indexed[rec.content_seq] = rec.comment_seq
        post_bookmark_indexed = {}
        for rec in post_bookmarks:
            post_bookmark_indexed[rec.post_seq] = rec.bookmark_seq        

        user_reactions_indexed = {}
        for react in self.reactions:
            query_params = [self.session.get_session_value("_profile_seq"),react]
            post_reactions = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_POST_LIKES",query_params,subs=subs)                
            for rec in post_reactions:
                if rec.post_seq not in user_reactions_indexed:
                    user_reactions_indexed[rec.post_seq] = {}
                if react not in user_reactions_indexed[rec.post_seq]:
                    user_reactions_indexed[rec.post_seq][react] = {"selected":"NO","count":0}
                user_reactions_indexed[rec.post_seq][react]["selected"] = "YES"

        qparams = ["LIKE"]
        like_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_LIKE_COUNTS",qparams,subs=subs)                    
        qparams = ["POST"]
        comment_counts = self.connDB.execute_prepared_stmt("sotrueappuser","GET_POST_COMMENT_COUNTS",qparams,subs=subs)            
        like_counts_indexed = dict()
        comment_counts_indexed = dict()        
        for like in like_counts:
            like_counts_indexed[like.post_seq] = like.count        
        for comment in comment_counts:
            comment_counts_indexed[comment.content_seq] = comment.count

        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)

        count = 1
        selected = int(self.params["sel_post_seq"]) if "sel_post_seq" in self.params else 1
        total = str(len(clips))
        for rec in clips:
            rec["fuzzy_image"] = rec["fuzzy_image"] if not rec["fuzzy_image"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(rec["profile_seq"]) + "/" + rec["fuzzy_image"],AppConfigs.s3_posts_folder, rec["s3_enabled"])
            rec["media_file"] = rec["media_file"] if not rec["media_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(rec["profile_seq"]) + "/" + rec["media_file"],AppConfigs.s3_posts_folder, rec["s3_enabled"])
            rec["media_cover"] = rec["media_cover"] if not rec["media_cover"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(rec["profile_seq"]) + "/" + rec["media_cover"],AppConfigs.s3_posts_folder, rec["s3_enabled"])
            rec["sequence"] = str(count) + "/" + total
            rec["selected"] = "YES" if selected == count else "NO"

            # Episode title and description are already included from the GET_SEASON_CLIPS query
            # as episode_title and episode_description fields

            rec["likes"] = 0 if rec["post_seq"] not in like_counts_indexed else like_counts_indexed[rec["post_seq"]]
            rec["comments"] = 0 if rec["post_seq"] not in comment_counts_indexed else comment_counts_indexed[rec["post_seq"]]
            rec["is_liked"] = 'YES' if rec["post_seq"] in post_likes_indexed else 'NO'
            rec["user_reactions"] = {} if  rec["post_seq"] not in user_reactions_indexed else user_reactions_indexed[rec["post_seq"]]
            rec["is_bookmarked"] = 'YES' if rec["post_seq"] in post_bookmark_indexed else 'NO'
            rec["is_commented"] = 'YES' if rec["post_seq"] in  post_comments_indexed else 'NO'

            count += 1

        # Get season details instead of episode details
        qparams = [self.params["season_seq"]]
        season_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_SEASON_DETAILS",qparams)

        # Get user data from the first clip's profile (assuming all clips in season belong to same user)
        if clips:
            qparams = [clips[0]["profile_seq"]]
            user_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_PROFILE_DETAILS",qparams)
        else:
            user_data = None

        response = self.create_response(clips,"UE011",addnl={
            "season_seq": self.params["season_seq"],
            "season_title": season_data[0].title if season_data else "",
            "season_description": season_data[0].description if season_data else "",
            "profile_seq": user_data[0].profile_seq if user_data else "",
            "user_handle": user_data[0].user_handle if user_data else "",
            "display_name": user_data[0].display_name if user_data else "",
            "is_verified": user_data[0].is_verified if user_data else "",
            "profile_picture": user_data[0].profile_picture if user_data and user_data[0].profile_picture else generic_code.create_media_url(SotrueAppConfig.profile_path + user_data[0].profile_picture,AppConfigs.s3_profiles_folder,user_data[0].s3_enabled) if user_data and user_data[0].profile_picture else ""
        })

        # Log response
        self.logger.log_message("=== GET_PLAYLIST_CLIPS RESPONSE (SUCCESS) ===")
        self.logger.log_message(f"Response clips count: {len(clips)}")
        self.logger.log_message(f"Response: {response}")

        return(response)


    def get_preferred_posts(self):
        
        post_seqs = [2470,2476,2477,2478,2479,2480,2461,2462,2463,2464,2465,2466,2467,2468]
        
        if post_seqs:
            subs = {"<POST_SEQ_LIST>":self.utils.convert_to_delim_str(post_seqs,",")}
            qparams = [self.session.get_session_value("_profile_seq")]
            posts = self.connDB.execute_prepared_stmt("sotrueappuser","CHECK_POST_VIEW",qparams,subs=subs)

            viewed_posts = []
            for rec in posts:
                viewed_posts.append(int(rec.post_seq))

            remaining_posts = set(post_seqs) - set(viewed_posts)
        
            return(list(remaining_posts))
        return([])
